<!-- 博客详情页 -->
{% section meta_keywords -%}
{% if Model.Blog.SeoKeyword_en and Model.Blog.SeoKeyword_en != "" %}
    <meta name="keywords" content="{{ Model.Blog.SeoKeyword_en }}">
{% elsif Model.Blog.Tag and Model.Blog.Tag != "" %}
    <meta name="keywords" content="{{ Model.Blog.Tag }}, {{ Model.Blog.Title }}">
{% else %}
    <meta name="keywords" content="{{ Model.Blog.Title }}, {{ Model.Blog.Author }}">
{% endif %}
{% endsection -%}
{% section meta_description -%}
{% if Model.Blog.SeoDescription_en and Model.Blog.SeoDescription_en != "" %}
    <meta name="description" content="{{ Model.Blog.SeoDescription_en }}">
{% elsif Model.Blog.BriefDescription and Model.Blog.BriefDescription != "" %}
    <meta name="description" content="{{ Model.Blog.BriefDescription }}">
{% else %}
{% endif %}
{% endsection -%}
{% section title -%}
{% if Model.Blog.SeoTitle_en and Model.Blog.SeoTitle_en != "" %}
    <title>{{ Model.Blog.SeoTitle_en }}</title>
{% else %}
    <title>{{ Model.Blog.Title }}</title>
{% endif %}
{% endsection -%}
<div class="blog-single-area py-90">
    <div class="container">
        <!-- 调试信息 -->
        {% comment %}<div class="alert alert-info">{% endcomment %}
        {% comment %}<p>Model 是否为 null: {% if Model == null %}是{% else %}否{% endif %}</p>{% endcomment %}
        {% comment %}<p>Model.Blog 是否为 null: {% if Model.Blog == null %}是{% else %}否{% endif %}</p>{% endcomment %}
        {% comment %}<p>Model.Content 是否为 null: {% if Model.Content == null %}是{% else %}否{% endif %}</p>{% endcomment %}
        {% comment %}<p>博客ID: {{ Model.BlogId }}</p>{% endcomment %}
        {% comment %}</div>{% endcomment %}
        <div class="row">
            <div class="col-lg-10 col-xxl-9 mx-auto">
                <div class="blog-single-wrap">
                    <div class="blog-single-content">
                        <div class="blog-thumb-img">
                            {% if Model.Blog.PicPath %}
                                <img class="blur-up lazyload blog-detail-image" data-src="{{ Model.Blog.PicPath }}" src="{{ Model.Blog.PicPath }}" alt="{{ Model.Blog.Title }}">
                            {% else %}
                                <img src="{{ static_path }}/assets/img/Blog/banner.jpg" alt="{{ Model.Blog.Title }}">
                            {% endif %}
                        </div>
                        <div class="blog-info">
                            <div class="blog-meta">
                                <div class="blog-meta-left">
                                    <ul>
                                        <li><i class="far fa-user"></i><a
                                                    href="#">{% if Model.Blog.Author != null %}{{ Model.Blog.Author }}{% else %}Jean R Gunter{% endif %}</a>
                                        </li>
                                        <li>
                                            <i class="far fa-comments"></i>{% if Model.Blog.Comments != null %}{{ Model.Blog.Comments }}{% else %}0{% endif %}
                                            {{ "blog.global.comments"|translate}}
                                        </li>
                                        <li>
                                            <i class="far fa-thumbs-up"></i>{% if Model.Blog.Praise != null %}{{ Model.Blog.Praise }}{% else %}0{% endif %}
                                            Like
                                        </li>
                                    </ul>
                                </div>
                                <div class="blog-meta-right">
                                    <a href="javascript:void(0)" class="share-link" onclick="shareBlog()"><i
                                                class="far fa-share-alt"></i>{{ "user.account.DIST_how_to_share"|translate}}</a>
                                </div>
                            </div>
                            <div class="blog-details">
                                <h3 class="blog-details-title mb-20">{{ Model.Blog.Title }}</h3>

                                <!-- 博客内容 -->
                                {% if Model.Content != null and Model.Content.Content != null and Model.Content.Content != '' %}
                                    {{ Model.Content.Content | raw }}
                                {% else %}
                                    <p class="mb-20">{{ Model.Blog.BriefDescription }}</p>
                                {% endif %}

                                <hr>

                                <!-- 标签 -->
                                {% if Model.Blog.TagsNameList != null and Model.Blog.TagsNameList.size > 0 %}
                                    <div class="blog-details-tags pb-20">
                                        <h5>{{ "blog.global.tags"|translate}}: </h5>
                                        <ul>
                                            {% for tagName in Model.Blog.TagsNameList %}
                                                <li><a href="/blog?tagName={{ tagName }}">{{ tagName }}</a></li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                {% endif %}
                            </div>

                            <!-- 作者信息 -->
                            <div class="blog-author">
                                <div class="blog-author-img">
                                    <img src="{{ static_path }}/assets/img/HearingAids/group5.jpg" alt="Author">
                                </div>
                                <div class="author-info">
                                    <h6>Author</h6>
                                    <h3 class="author-name">{% if Model.Blog.Author != null %}{{ Model.Blog.Author }}{% else %}Admin{% endif %}</h3>
                                    <p>{{ Model.Blog.BriefDescription }}</p>
                                    <div class="author-social">
                                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                                        <a href="#"><i class="fab fa-twitter"></i></a>
                                        <a href="#"><i class="fab fa-instagram"></i></a>
                                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 评论区 -->
                        <div class="blog-comments mb-0">
                            <h3>{{ "blog.global.comments"|translate}} {% if Model.Blog.Comments > 0 %}({{ Model.Blog.Comments }}){% endif %}</h3>
                            <div class="blog-comments-wrap">
                                {% if Model.Blog.Reviews != null and Model.Blog.Reviews.size > 0 %}
                                    {% for review in Model.Blog.Reviews %}
                                        <div class="blog-comments-item">
                                            <img src="{{ static_path }}/assets/img/HearingAids/group5.jpg" alt="thumb">
                                            <div class="blog-comments-content">
                                                <h5>{{ review.Name }}</h5>
                                                <span><i class="far fa-clock"></i> 
                                                {% assign acc_time_seconds = review.AccTime | times: 1 %}
                                                    {{ acc_time_seconds | date: '%B %d, %Y at %I:%M %p' }}
                                            </span>
                                                <p>{{ review.Content }}</p>
                                                <a href="javascript:void(0)"
                                                   onclick="replyToComment({{ review.RId }}, '{{ review.Name }}', null)">
                                                    <i class="far fa-reply"></i> {{ "user.account.reply_btn"|translate}}
                                                </a>
                                            </div>
                                        </div>
                                        {% if review.Replies != null and review.Replies.size > 0 %}
                                            {% for reply in review.Replies %}
                                                <div class="blog-comments-item blog-comments-reply">
                                                    <img src="{{ static_path }}/assets/img/HearingAids/group5.jpg"
                                                         alt="thumb">
                                                    <div class="blog-comments-content">
                                                        <h5>{{ reply.ReplyName }} {% if reply.IsAdmin %}<span
                                                                    class="badge bg-primary"
                                                                    style="font-size: 0.7em; vertical-align: middle;">管理员</span>{% endif %}
                                                        </h5>
                                                        <span><i class="far fa-clock"></i> 
                                                        {% assign reply_time_seconds = reply.ReplyTime | times: 1 %}
                                                            {{ reply_time_seconds | date: '%B %d, %Y at %I:%M %p' }}
                                                    </span>
                                                        <p>
                                                            {% if reply.ReplyToName != null and reply.ReplyToName != '' %}
                                                                <span class="reply-to-tag">@{{ reply.ReplyToName }}</span>
                                                            {% endif %}
                                                            {{ reply.Content }}
                                                        </p>
                                                        <a href="javascript:void(0)"
                                                           onclick="replyToComment({{ review.RId }}, '{{ reply.ReplyName }}', {{ reply.ReplyId }})"><i
                                                                    class="far fa-reply"></i> {{ "user.account.reply_btn"|translate}}</a>
                                                    </div>
                                                </div>
                                            {% endfor %}
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    <div class="alert alert-info mt-20">
                                        <p>{{ "products.goods.no_review_data"|translate}} . {{ "blog.global.leaveAReply"|translate}} !</p>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="blog-comments-form">
                                <h3>{{ "blog.global.leaveAReply"|translate}}</h3>
                                <div id="reply-info" class="alert alert-info mb-3" style="display: none;">
                                    <p id="reply-to-text">{{ "user.account.reply_btn"|translate}}: <span id="reply-to-name"></span></p>
                                    <button type="button" class="btn btn-sm btn-outline-secondary"
                                            onclick="cancelReply()">{{ "web.global.cancel"|translate}}{{ "user.account.reply_btn"|translate}}
                                    </button>
                                </div>
                                <form id="comment-form">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <input type="text" class="form-control" id="comment-name"
                                                       placeholder="{{ "products.goods.write_your_name"|translate}}*" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <input type="email" class="form-control" id="comment-email"
                                                       placeholder="{{ "web.global.newsletter_your_email"|translate}}*">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <textarea class="form-control" id="comment-content" rows="5"
                                                          placeholder="{{ "products.goods.write_your_review"|translate}}*" required></textarea>
                                            </div>
                                            <!-- 添加验证码隐藏字段 -->
                                            <input type="hidden" id="captchaVerifyParam" name="captchaVerifyParam" value="">
                                            <!-- 添加验证码容器 -->
                                            <div id="captcha-element" class="mb-3"></div>
                                            <button type="button" class="theme-btn" id="comment-submit-blog-btn">{{ "products.goods.writeReview"|translate}} <i class="far fa-paper-plane"></i></button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <!-- 评论区代码结束 -->
                    </div>
                </div>
            </div>
        </div>
        <!-- 添加回复标签的样式 -->
        <style>
            .reply-to-tag {
                color: #007bff;
                font-weight: 600;
                margin-right: 5px;
            }

            .blog-comments-reply {
                margin-left: 40px;
                position: relative;
            }

            .blog-comments-reply::before {
                content: "";
                position: absolute;
                left: -20px;
                top: 15px;
                width: 20px;
                height: 1px;
                background-color: #ddd;
            }
        </style>
    </div>
</div>

<!-- 引入自定义消息弹窗脚本 -->
<script src="/js/Pop-ups/frame-message.js"></script>
<!-- 引入阿里云验证码2.0 SDK -->
<script src="/js/AliyunCaptcha.js"></script>
<!-- 引入验证码核心脚本 -->
<script src="/businessJs/aliyun-captcha-core.js"></script>
<script>
    // 分享博客
    function shareBlog() {
        if (navigator.share) {
            navigator.share({
                title: '{{ Model.Blog.Title }}',
                text: '{{ Model.Blog.BriefDescription }}',
                url: window.location.href
            }).then(() => {
                console.log('分享成功');
            }).catch((error) => {
                console.log('分享失败:', error);
            });
        } else {
            customize_pop.info('Your browser does not support Web Share API, please copy the link manually to share');
        }
    }

    // 页面加载时初始化验证码
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化验证码
        if (typeof YseCaptcha !== 'undefined') {
            YseCaptcha.init({
                onSuccess: function() {
                    // 验证成功后处理表单提交
                    submitComment();
                },
                onClose: function() {
                    // 用户关闭或取消验证码时恢复按钮状态
                    resetSubmitButton();
                }
            });
        } else {
            console.error("YseCaptcha 未加载");
        }
        
        // 绑定提交按钮事件
        document.getElementById('comment-submit-blog-btn').addEventListener('click', function() {
            // 检查表单
            const name = document.getElementById('comment-name').value;
            const content = document.getElementById('comment-content').value;
            
            if (!name.trim()) {
                customize_pop.warning('Please enter your name',null, null, {showIcon: false});
                return;
            }

            if (!content.trim()) {
                customize_pop.warning('Please enter comment content',null, null, {showIcon: false});
                return;
            }
            
            // 显示提交中的状态
            const submitBtn = document.getElementById('comment-submit-blog-btn');
            submitBtn.disabled = true;
            
            // 触发验证码
            if (typeof YseCaptcha !== 'undefined') {
                YseCaptcha.verify();
            } else {
                customize_pop.warning('Captcha service not initialized, please refresh the page and try again');
                resetSubmitButton();
            }
        });
    });
    
    // 重置提交按钮状态
    function resetSubmitButton() {
        const submitBtn = document.getElementById('comment-submit-blog-btn');
        submitBtn.innerHTML = '{{ "products.goods.writeReview"|translate}} <i class="far fa-paper-plane"></i>';
        submitBtn.disabled = false;
    }

    // 提交评论函数
    function submitComment() {
        // 获取表单数据
        const name = document.getElementById('comment-name').value;
        const email = document.getElementById('comment-email').value;
        const content = document.getElementById('comment-content').value;
        const captchaVerifyParam = YseCaptcha.getVerifyParam();
        const blogId = {{ Model.BlogId }};

        // 验证表单数据
        if (!name.trim()) {
            customize_pop.warning('Please enter your name');
            resetSubmitButton();
            return;
        }

        if (!content.trim()) {
            customize_pop.warning('Please enter comment content');
            resetSubmitButton();
            return;
        }

        // 验证验证码
        if (!captchaVerifyParam) {
            customize_pop.warning('Please complete captcha verification');
            resetSubmitButton();
            return;
        }

        // 显示提交中的状态
        const submitBtn = document.getElementById('comment-submit-blog-btn');
        submitBtn.innerHTML = 'Committing... <i class="fas fa-spinner fa-spin"></i>';
        submitBtn.disabled = true;

        // 检查是否在回复评论
        const commentForm = document.getElementById('comment-form');
        const replyToId = commentForm.dataset.replyTo;

        if (replyToId) {
            // 提交回复
            const replyData = {
                name: name,
                content: content,
                isAdmin: false,
                replyToId: parseInt(commentForm.dataset.replyToId || "0"),
                replyToName: commentForm.dataset.replyToName || "",
                captchaVerifyParam: captchaVerifyParam // 添加验证码参数
            };

            fetch(`/api/blog/comment/${replyToId}/reply`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(replyData)
            })
                .then(response => response.json())
                .then(data => {
                    // 恢复按钮状态
                    resetSubmitButton();

                    if (data.success) {
                        customize_pop.success(data.message, function () {
                            // 清空表单和回复标记
                            document.getElementById('comment-name').value = '';
                            document.getElementById('comment-email').value = '';
                            document.getElementById('comment-content').value = '';
                            // 重置验证码
                            YseCaptcha.reset();
                            delete commentForm.dataset.replyTo;
                            cancelReply();

                            // 刷新页面以显示新回复
                            setTimeout(() => {
                                location.reload();
                            }, 1000);
                        });
                    } else {
                        customize_pop.error(data.message || 'Reply submission failed, please try again later');
                    }
                })
                .catch(error => {
                    // 恢复按钮状态
                    resetSubmitButton();

                    console.error('提交回复时出错:', error);
                    customize_pop.error('Reply submission failed, please try again later');
                });
        } else {
            // 提交新评论
            const commentData = {
                blogId: blogId,
                name: name,
                email: email,
                content: content,
                captchaVerifyParam: captchaVerifyParam // 添加验证码参数
            };

            fetch('/api/blog/comment/submit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(commentData)
            })
                .then(response => response.json())
                .then(data => {
                    // 恢复按钮状态
                    resetSubmitButton();

                    if (data.success) {
                        customize_pop.success(data.message, function () {
                            // 清空表单
                            document.getElementById('comment-name').value = '';
                            document.getElementById('comment-email').value = '';
                            document.getElementById('comment-content').value = '';
                            // 重置验证码
                            YseCaptcha.reset();

                            // 刷新页面以显示新评论
                            setTimeout(() => {
                                location.reload();
                            }, 1000);
                        });
                    } else {
                        customize_pop.error(data.message || 'Comment submission failed, please try again later');
                    }
                })
                .catch(error => {
                    // 恢复按钮状态
                    resetSubmitButton();

                    console.error('提交评论时出错:', error);
                    customize_pop.error('Comment submission failed, please try again later');
                });
        }
    }

    // 回复评论函数
    function replyToComment(reviewId, name, replyId) {
        // 滚动到评论表单
        const commentForm = document.getElementById('comment-form');
        commentForm.scrollIntoView({behavior: 'smooth'});

        // 显示回复提示
        const replyInfo = document.getElementById('reply-info');
        const replyToName = document.getElementById('reply-to-name');
        replyInfo.style.display = 'block';
        replyToName.textContent = name;

        // 聚焦到表单
        document.getElementById('comment-name').focus();

        // 存储正在回复的评论ID
        commentForm.dataset.replyTo = reviewId;

        // 存储回复目标ID和名称（如果有）
        commentForm.dataset.replyToId = replyId ? replyId : 0;
        commentForm.dataset.replyToName = name || "";
    }

    // 取消回复函数
    function cancelReply() {
        // 隐藏回复提示
        document.getElementById('reply-info').style.display = 'none';

        // 清除回复ID和回复目标信息
        const commentForm = document.getElementById('comment-form');
        delete commentForm.dataset.replyTo;
        commentForm.dataset.replyToId = "0";
        commentForm.dataset.replyToName = "";
    }
</script>

<!-- 导入图片处理助手 -->
<script src="/businessJs/imageUrlHelper.js"></script>
<script>
// 页面加载完成后处理所有图片URL
document.addEventListener('DOMContentLoaded', function() {
    // 处理博客详情页面中的主图
    const blogDetailImages = document.querySelectorAll('.blog-detail-image');
    blogDetailImages.forEach(function(img) {
        if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {
            // 为博客详情页主图使用大图尺寸
            img.src = ImageUrlHelper.getLargeUrl(img.src);
        }
        if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {
            img.dataset.src = ImageUrlHelper.getLargeUrl(img.dataset.src);
        }
    });

    // 处理博客内容中的图片
    const contentImages = document.querySelectorAll('.rte img');
    contentImages.forEach(function(img) {
        if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {
            // 为内容图片使用中等尺寸
            img.src = ImageUrlHelper.getMediumUrl(img.src);
        }
    });
});
</script>