<!--<div>博客</div>-->
<div class="page-support" id="blog-index-container">
    <img class="w-100" src="{{ static_path }}/assets/img/Blog/banner.jpg" alt="Blog">
    <div>
        <div class="container pt-40 pb-40">
            <h1 class="text-center pt-40 pb-40" style="text-transform: uppercase;">{{ "blog.global.blog"|translate}} {{ "blog.global.searchNote"|translate}}</h1>
            <div class="row" style="justify-content:center;">
                <div class="col-lg-6 col-md-12 col-sm-12">
                    <div class="help-search-form">
                        <div class="form-group">
                            <input type="text" id="search-keyword"
                                   style="border-top: 0; border-left: 0; border-right: 0; width: 80%; border-radius: 0; "
                                   class="form-control" placeholder="{{ "blog.global.searchNote"|translate}}..." value="{{ Model.Keyword }}">
                            <button type="button" class="p-0" onclick="searchBlog()"><span style="padding: 8px 30px; text-transform: uppercase;" class="theme-btn">{{ "blog.global.searchNote"|translate}}</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 调试信息 -->
    {% comment %}<div class="container">{% endcomment %}
    {% comment %}<div class="alert alert-info">{% endcomment %}
    {% comment %}<p>Model 是否为 null: {% if Model == null %}是{% else %}否{% endif %}</p>{% endcomment %}
    {% comment %}<p>Model.Blogs 是否为 null: {% if Model.Blogs == null %}是{% else %}否{% endif %}</p>{% endcomment %}
    {% comment %}<p>Model.Blogs 数量: {% if Model.Blogs != null %}{{ Model.Blogs | size }}{% else %}0{% endif %}</p>{% endcomment %}
    {% comment %}<p>当前页: {{ Model.CurrentPage }}</p>{% endcomment %}
    {% comment %}<p>总页数: {{ Model.TotalPages }}</p>{% endcomment %}
    {% comment %}</div>{% endcomment %}
    {% comment %}</div>{% endcomment %}

    <div class="container pt-40 pb-40">
        <div class="row" id="blog-list-container">
            {% if Model.Blogs != null and Model.Blogs.size > 0 %}
                {% for blog in Model.Blogs %}
                    <div class="col-lg-3 col-md-12 col-sm-12">
                        <div class="blog-item">
                            <div class="blog-item-img">
                                {% comment %}<img class="w-100"{% endcomment %}
                                     {% comment %}src="{% if blog.PicPath != null and blog.PicPath != '' %}{{ static_path }}{{ blog.PicPath }}{% else %}{{ static_path }}/assets/img/HearingAids/2.png{% endif %}"{% endcomment %}
                                     {% comment %}alt="{{ blog.Title }}">{% endcomment %}
                                <img class="w-100" src="{{static_path}}/assets/img/HearingAids/4.png" alt="HearingAids">
                                
                            </div>

                            <div class="help-content">
                                <h4>{{ blog.Title }}</h4>
                                <span class="blog-date">
                                    <i class="far fa-calendar-alt"></i>
                                    {% if blog.StandardAccTime != null %}
                                    {{ StandardAccTime }}
                                    {% else %}
                                    Aug 12, 2024
                                    {% endif %}
                                </span>
                                <p>{{ blog.BriefDescription }}</p>
                                <a href="/blog/{{ blog.PageUrl }}" 
                                  class="read-more-btn">
                                    {{ "blog.global.readMore"|translate}}
                                    >>
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="col-12 text-center">
                    <p>暂无博客文章</p>
                </div>
            {% endif %}
        </div>

        <div class="pagination-area mt-60">
            <div aria-label="Page navigation example">
                {% if Model.TotalPages > 0 %}
                    <ul class="pagination" id="blog-pagination">
                        {% assign prevPage = Model.CurrentPage | minus: 1 %}
                        <li class="page-item {% if Model.CurrentPage == 1 %}disabled{% endif %}">
                            <a class="page-link" href="{% unless Model.CurrentPage == 1 %}/blog?page={{ prevPage }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.CateId %}&cateId={{ Model.CateId }}{% endif %}{% if Model.TagId %}&tagId={{ Model.TagId }}{% endif %}{% else %}javascript:void(0){% endunless %}"
                               aria-label="Previous">
                                <span aria-hidden="true"><i class="far fa-arrow-left"></i></span>
                            </a>
                        </li>

                        {% if Model.TotalPages <= 5 %}
                            {% for i in (1..Model.TotalPages) %}
                                <li class="page-item {% if i == Model.CurrentPage %}active{% endif %}">
                                    <a class="page-link" href="/blog{% if i > 1 %}?page={{ i }}{% endif %}{% if Model.Keyword %}{% if i > 1 %}&{% else %}?{% endif %}keyword={{ Model.Keyword }}{% endif %}{% if Model.CateId %}{% if i > 1 or Model.Keyword %}&{% else %}?{% endif %}cateId={{ Model.CateId }}{% endif %}{% if Model.TagId %}{% if i > 1 or Model.Keyword or Model.CateId %}&{% else %}?{% endif %}tagId={{ Model.TagId }}{% endif %}">{{ i }}</a>
                                </li>
                            {% endfor %}
                        {% else %}
                            <li class="page-item {% if Model.CurrentPage == 1 %}active{% endif %}">
                                <a class="page-link" href="/blog{% if Model.Keyword %}?keyword={{ Model.Keyword }}{% endif %}{% if Model.CateId %}{% if Model.Keyword %}&{% else %}?{% endif %}cateId={{ Model.CateId }}{% endif %}{% if Model.TagId %}{% if Model.Keyword or Model.CateId %}&{% else %}?{% endif %}tagId={{ Model.TagId }}{% endif %}">1</a>
                            </li>

                            {% if Model.CurrentPage > 3 %}
                                <li class="page-item"><span class="page-link">...</span></li>
                            {% endif %}

                            {% if Model.CurrentPage > 2 %}
                                {% assign prevPage = Model.CurrentPage | minus: 1 %}
                                <li class="page-item">
                                    <a class="page-link" href="/blog?page={{ prevPage }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.CateId %}&cateId={{ Model.CateId }}{% endif %}{% if Model.TagId %}&tagId={{ Model.TagId }}{% endif %}">{{ prevPage }}</a>
                                </li>
                            {% endif %}

                            {% if Model.CurrentPage != 1 and Model.CurrentPage != Model.TotalPages %}
                                <li class="page-item active">
                                    <a class="page-link" href="javascript:void(0)">{{ Model.CurrentPage }}</a>
                                </li>
                            {% endif %}

                            {% assign totalPagesMinus1 = Model.TotalPages | minus: 1 %}
                            {% if Model.CurrentPage < totalPagesMinus1 %}
                                {% assign nextPage = Model.CurrentPage | plus: 1 %}
                                <li class="page-item">
                                    <a class="page-link" href="/blog?page={{ nextPage }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.CateId %}&cateId={{ Model.CateId }}{% endif %}{% if Model.TagId %}&tagId={{ Model.TagId }}{% endif %}">{{ nextPage }}</a>
                                </li>
                            {% endif %}

                            {% assign twoLess = Model.TotalPages | minus: 2 %}
                            {% if Model.CurrentPage < twoLess %}
                                <li class="page-item"><span class="page-link">...</span></li>
                            {% endif %}

                            <li class="page-item {% if Model.CurrentPage == Model.TotalPages %}active{% endif %}">
                                <a class="page-link" href="/blog?page={{ Model.TotalPages }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.CateId %}&cateId={{ Model.CateId }}{% endif %}{% if Model.TagId %}&tagId={{ Model.TagId }}{% endif %}">{{ Model.TotalPages }}</a>
                            </li>
                        {% endif %}

                        {% assign nextPage = Model.CurrentPage | plus: 1 %}
                        <li class="page-item {% if Model.CurrentPage == Model.TotalPages %}disabled{% endif %}">
                            <a class="page-link" href="{% unless Model.CurrentPage == Model.TotalPages %}/blog?page={{ nextPage }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.CateId %}&cateId={{ Model.CateId }}{% endif %}{% if Model.TagId %}&tagId={{ Model.TagId }}{% endif %}{% else %}javascript:void(0){% endunless %}"
                               aria-label="Next">
                                <span aria-hidden="true"><i class="far fa-arrow-right"></i></span>
                            </a>
                        </li>
                    </ul>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>

    // 搜索博客函数
    function searchBlog() {
        const keyword = document.getElementById('search-keyword').value;
        let url = '/blog';

        if (keyword) {
            url += `?keyword=${encodeURIComponent(keyword)}`;
        }

        {% if Model.CateId != null and Model.CateId != '' %}
        url += (url.includes('?') ? '&' : '?') + `cateId={{ Model.CateId }}`;
        {% endif %}

        {% if Model.TagId != null and Model.TagId != '' %}
        url += (url.includes('?') ? '&' : '?') + `tagId={{ Model.TagId }}`;
        {% endif %}

        // 直接跳转到搜索结果页面
        window.location.href = url;
    }
    
    // 为搜索输入框添加回车键搜索功能
    document.getElementById('search-keyword').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            searchBlog();
        }
    });
</script>