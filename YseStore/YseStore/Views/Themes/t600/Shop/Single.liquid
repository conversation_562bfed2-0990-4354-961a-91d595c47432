{% section meta_keywords -%}
{% if Model.Product.ProductSeo and Model.Product.ProductSeo.SeoKeyword_en and Model.Product.ProductSeo.SeoKeyword_en != "" %}
    <meta name="keywords" content="{{ Model.Product.ProductSeo.SeoKeyword_en }}">
{% elsif Model.Product.Tags and Model.Product.Tags.size > 0 %}
    <meta name="keywords"
          content="{% for tag in Model.Product.Tags %}{{ tag.Name }}{% unless forloop.last %}, {% endunless %}{% endfor %}, {{ Model.Product.ProductName }}">
{% else %}
    <meta name="keywords" content="{{ Model.Product.ProductName }}, {{ Model.Product.BrandName }}">
{% endif %}
{% endsection -%}
{% section meta_description -%}
{% if Model.Product.ProductSeo and Model.Product.ProductSeo.SeoDescription_en and Model.Product.ProductSeo.SeoDescription_en != "" %}
    <meta name="description" content="{{ Model.Product.ProductSeo.SeoDescription_en }}">
{% elsif Model.Product.BriefDescription and Model.Product.BriefDescription != "" %}
    <meta name="description" content="{{ Model.Product.BriefDescription }}">
{% else %}
{% endif %}
{% endsection -%}
{% section title -%}
{% if Model.Product.ProductSeo and Model.Product.ProductSeo.SeoTitle_en and Model.Product.ProductSeo.SeoTitle_en != "" %}
    <title>{{ Model.Product.ProductSeo.SeoTitle_en }}</title>
{% else %}
    <title>{{ Model.Product.ProductName }}</title>
{% endif %}
{% endsection -%}

<div class="shop-single py-90">
    <div class="container">
        <div class="row">
            <div class="col-md-9 col-lg-6 col-xxl-5">
                <div class="shop-single-gallery">
                    {% if Model.Product.VideoUrl != null and Model.Product.VideoUrl != "" %}
                        <a class="shop-single-video popup-youtube" href="{{ Model.Product.VideoUrl }}"
                           data-tooltip="tooltip" title="Watch Video">
                            <i class="far fa-play"></i>
                        </a>
                    {% endif %}
                    <div class="flexslider-thumbnails">
                        <ul class="slides">
                            {% if Model.Product.ProductImages != null and Model.Product.ProductImages.size > 0 %}
                                {% for image in Model.Product.ProductImages %}
                                    <li data-thumb="{{ image.PicPath }}" rel="adjustX:10, adjustY:">
                                        <img src="{{ image.PicPath }}"
                                             alt="{{ Model.Product.ProductName }}"/>
                                    </li>
                                {% endfor %}
                            {% else %}
                                <li data-thumb="/assets/img/product/01.png" rel="adjustX:10, adjustY:">
                                    <img src="{{ Model.Product.PicPath }}"
                                         alt="{{ Model.Product.ProductName }}"/>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-lg-6 col-xxl-6">
                <div class="shop-single-info">
                    <h4 class="shop-single-title">{{ Model.Product.ProductName }}</h4>
                    <div class="shop-single-rating">
                        {% assign fullStars = Model.Product.Rating | floor %}
                        {% assign halfStar = Model.Product.Rating | minus: fullStars %}
                        {% assign nextStar = fullStars | plus: 1 %}
                        {% for i in (1..5) %}
                            {% if i <= fullStars %}
                                <i class="fas fa-star"></i>
                            {% elsif halfStar >= 0.5 and i == nextStar %}
                                <i class="fas fa-star-half-alt"></i>
                            {% else %}
                                <i class="far fa-star"></i>
                            {% endif %}
                        {% endfor %}
                        <a href="#nav-tab"><span
                                    class="rating-count"> ({{ Model.Product.ReviewCount }} {{ "products.goods.customer_review" | translate }})</span></a>
                    </div>
                    <div class="shop-single-price">
                        {% if Model.Product.OriginalPrice != null and Model.Product.OriginalPrice > Model.Product.Price %}
                            <del>{{ Model.Product.OriginalPriceFormat }}</del>
                            <span class="amount">{{ Model.Product.PriceFormat }}</span>
                            {% assign discountPercent = Model.Product.OriginalPrice | minus: Model.Product.Price | times: 100 | divided_by: Model.Product.OriginalPrice | round %}
                            <span class="discount-percentage">{{ discountPercent }}% Off</span>
                        {% else %}
                            <span class="amount">{{ Model.Product.PriceFormat }}</span>
                        {% endif %}
                    </div>
                    {% if Model.Product.PromotionPrice != 0 %}
                        <div class="shop-single-price">
                            <span style="font-size:18px;">Promotional Price ：</span>
                            <span style="color: var(--color-red2);">{{ Model.Product.PromotionPriceFormat }}</span>
                        </div>
                    {% endif %}


                    <p class="mb-3">
                        {{ Model.Product.BriefDescription | raw }}
                    </p>
                    <div class="shop-single-cs">
                        <div class="row">
                            <div class="col-md-3 col-lg-4 col-xl-3">
                                <div class="shop-single-size">
                                    <h6>{{ "web.global.qty" | translate }}</h6>
                                    <div class="shop-cart-qty">
                                        <button class="minus-btn">
                                            <i class="fal fa-minus"></i>
                                        </button>
                                        <input class="quantity" type="text" value="1" disabled="">
                                        <button class="plus-btn">
                                            <i class="fal fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {% comment %} 动态渲染所有属性，不再硬编码特定属性类型 {% endcomment %}
                            {% if Model.Product.DynamicAttributes != null and Model.Product.DynamicAttributes.size > 0 %}
                                {% for attribute in Model.Product.DynamicAttributes %}
                                    {% assign attributeName = attribute[0] %}
                                    {% assign attributeOptions = attribute[1] %}

                                    {% if attributeOptions != null and attributeOptions.size > 0 %}
                                        {% comment %} 检查是否有颜色代码，如果有则渲染为颜色选择器 {% endcomment %}
                                        {% assign hasColorCode = false %}
                                        {% for option in attributeOptions %}
                                            {% if option.ColorCode != null and option.ColorCode != "" %}
                                                {% assign hasColorCode = true %}
                                                {% break %}
                                            {% endif %}
                                        {% endfor %}

                                        {% if hasColorCode %}
                                            {% comment %} 渲染颜色选择器 {% endcomment %}
                                            <div class="col-md-6 col-lg-12 col-xl-6">
                                                <div class="shop-single-color">
                                                    <h6>{{ attributeName }}</h6>
                                                    <ul class="shop-checkbox-list color">
                                                        {% for option in attributeOptions %}
                                                            <li>
                                                                <div class="form-check">
                                                                    <input class="form-check-input" type="checkbox"
                                                                           id="{{ attributeName | downcase }}{{ forloop.index }}"
                                                                           data-attribute="{{ attributeName }}"
                                                                           data-option-id="{{ option.Id }}"
                                                                           data-option-name="{{ option.Name }}"
                                                                           {% if forloop.first %}checked{% endif %}>
                                                                    <label class="form-check-label"
                                                                           for="{{ attributeName | downcase }}{{ forloop.index }}">
                                                    <span style="background-color: {{ option.ColorCode }}"
                                                          title="{{ option.Name }}"></span>
                                                                    </label>
                                                                </div>
                                                            </li>
                                                        {% endfor %}
                                                    </ul>
                                                </div>
                                            </div>
                                        {% else %}
                                            {% comment %} 渲染下拉选择器（适用于尺寸等其他属性） {% endcomment %}
                                            <div class="col-md-3 col-lg-4 col-xl-3">
                                                <div class="shop-single-size">
                                                    <h6>{{ attributeName }}</h6>
                                                    <select class="select" data-attribute="{{ attributeName }}"
                                                            id="select_t600">
                                                        <option value="">Choose {{ attributeName }}</option>
                                                        {% for option in attributeOptions %}
                                                            <option value="{{ option.Id }}"
                                                                    data-option-name="{{ option.Name }}"
                                                                    {% if forloop.first %}selected{% endif %}>
                                                                {{ option.Name }}
                                                            </option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>

                    {% comment %} 仓库选择区域 {% endcomment %}
                    {% if Model.Product.OptionalWarehouses != null and Model.Product.OptionalWarehouses.size > 0 %}
                        <div class="shop-single-cs">
                            <div class="row">
                                <div class="col-md-6 col-lg-12 col-xl-6">
                                    <div class="shop-single-warehouse">
                                        <h6>仓库选择</h6>
                                        {% if Model.Product.OptionalWarehouses.size > 0 %}
                                            {% comment %} 多个仓库时显示选择器 {% endcomment %}
                                            <ul class="shop-checkbox-list warehouse">
                                                {% for warehouse in Model.Product.OptionalWarehouses %}
                                                    <li>
                                                        <div class="form-check">
                                                            <input class="form-check-input warehouseInput"
                                                                   type="checkbox"
                                                                   id="warehouse{{ forloop.index }}"
                                                                   data-warehouse-id="{{ warehouse.OvId }}"
                                                                   data-warehouse-name="{{ warehouse.Name }}"
                                                                   {% if forloop.first %}checked{% endif %}>
                                                            <label class="form-check-label"
                                                                   for="warehouse{{ forloop.index }}">
                                                                <span>{{ warehouse.Name }}</span>
                                                            </label>
                                                        </div>
                                                    </li>
                                                {% endfor %}
                                            </ul>
                                        {% else %}
                                            {% comment %} 只有一个仓库时自动选中 {% endcomment %}
                                            <input type="hidden" class="warehouseInput"
                                                   data-warehouse-id="{{ Model.Product.OptionalWarehouses[0].OvId }}"
                                                   data-warehouse-name="{{ Model.Product.OptionalWarehouses[0].Name }}"
                                                   checked>
                                            <span class="single-warehouse">{{ Model.Product.OptionalWarehouses[0].Name }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <div class="shop-single-sortinfo">
                        <ul>
                            <li>
                                {{ "user.account.order_status" | translate }}:
                                <span>{% if Model.Product.IsInStock %}Available{% else %}Out of Stock{% endif %}</span>
                            </li>
                            <li>
                                {{ "products.goods.sku" | translate }}: <span>{{ Model.Product.Sku }}</span>
                            </li>
                            <li>
                                {{ "blog.global.blog_category" | translate }}:
                                <span>{{ Model.Product.CategoryName }}</span>
                            </li>
                            <li>
                                Brand: <a href="#">{{ Model.Product.BrandName }}</a>
                            </li>
                            <li>
                                {{ "blog.global.tags" | translate }}:
                                {% if Model.Product.Tags != null and Model.Product.Tags.size > 0 %}
                                    {% for tag in Model.Product.Tags %}
                                        <a href="#">{{ tag.Name }}</a>{% unless forloop.last %},{% endunless %}
                                    {% endfor %}
                                {% else %}
                                    <span>No tags</span>
                                {% endif %}
                            </li>
                        </ul>
                    </div>
                    <div class="shop-single-action">
                        <div class="row align-items-center">
                            <div class="col-md-6 col-lg-12 col-xl-6">
                                <div class="shop-single-btn">
                                    {% if Model.Product.IsInStock %}
                                        <a href="#" class="theme-btn add-to-cart-btn"
                                           data-product-id="{{ Model.Product.ProductId }}">
                                            <span class="far fa-shopping-bag"></span>{{ "products.goods.addToCart" | translate }}
                                        </a>
                                    {% endif %}
                                    <a href="#" class="theme-btn theme-btn2 add-to-wishlist" data-tooltip="tooltip"
                                       title="{{ "products.goods.addToFavorites" | translate }}"
                                       data-product-id="{{ Model.Product.ProductId }}"
                                       data-is-favorited="{{ Model.Product.IsFavorited }}">
                                        {% if Model.Product.IsFavorited %}
                                            <span class="fas fa-heart"></span>
                                        {% else %}
                                            <span class="far fa-heart"></span>
                                        {% endif %}
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-12 col-xl-6">
                                <div class="shop-single-share">
                                    <span>Share:</span>
                                    <a href="#"><i class="fab fa-facebook-f"></i></a>
                                    <a href="#"><i class="fab fa-x-twitter"></i></a>
                                    <a href="#"><i class="fab fa-linkedin-in"></i></a>
                                    <a href="#"><i class="fab fa-pinterest-p"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 调试面板 -->
        {% comment %}<div class="debug-info" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 20px 0;">{% endcomment %}
        {% comment %}<h6 style="color: #495057; margin-bottom: 10px;">调试信息</h6>{% endcomment %}
        {% comment %}<div style="font-size: 14px; color: #6c757d;">{% endcomment %}
        {% comment %}<div><strong>匹配模式:</strong> <span id="debugMode">{% if Model.Product.IsCombination == 0 %}单规格{% elsif Model.Product.IsCombination == 1 %}多规格(组合匹配){% elsif Model.Product.IsCombination == 2 %}多规格加价(单独匹配){% else %}未知({{ Model.Product.IsCombination }}){% endif %}</span></div>{% endcomment %}
        {% comment %}<div><strong>选中的变体组合:</strong> <span id="debugSelectedAttributes">未选择</span></div>{% endcomment %}
        {% comment %}<div><strong>匹配的VariantsId:</strong> <span id="debugVariantsId">未匹配</span></div>{% endcomment %}
        {% comment %}<div><strong>选中的仓库:</strong> <span id="debugWarehouse">未选择</span></div>{% endcomment %}
        {% comment %}</div>{% endcomment %}
        {% comment %}</div>{% endcomment %}

        <!-- 隐藏字段用于存储匹配结果 -->
        <input type="hidden" id="selectedVariantsCombination" name="selectedVariantsCombination" value="">
        <input type="hidden" id="matchedVariantsId" name="matchedVariantsId" value="">

        <div class="shop-single-details">
            <nav>
                <div class="nav nav-tabs" id="nav-tab" role="tablist">
                    <!-- 从 Model.ProductSwitches 中动态生成所有面板标签 -->
                    {% if Model.ProductSwitches != null and Model.ProductSwitches.size > 0 %}
                        {% for switch in Model.ProductSwitches %}
                            <button class="nav-link {% if forloop.first %}active{% endif %}"
                                    id="nav-tab-{{ switch.SId }}"
                                    data-id="{{ switch.DataId }}"
                                    data-bs-toggle="tab"
                                    data-bs-target="#tab-{{ switch.SId }}"
                                    type="button"
                                    role="tab"
                                    aria-controls="tab-{{ switch.SId }}"
                                    aria-selected="{% if forloop.first %}true{% else %}false{% endif %}">
                                {{ switch.Name }}
                            </button>
                        {% endfor %}
                    {% endif %}
                </div>
            </nav>
            <div class="tab-content" id="nav-tabContent">
                <!-- 动态生成面板内容，对 Description 和 Reviews 特殊处理 -->
                {% if Model.ProductSwitches != null and Model.ProductSwitches.size > 0 %}
                    {% for switch in Model.ProductSwitches %}
                        <div class="tab-pane fade {% if forloop.first %}show active{% endif %}"
                             id="tab-{{ switch.SId }}"
                             data-id="{{ switch.DataId }}"
                             role="tabpanel"
                             aria-labelledby="nav-tab-{{ switch.SId }}">

                            <!-- 如果是 Description 面板，显示原有的产品描述内容 -->
                            {% if switch.Identity == "Description" %}
                                <div class="shop-single-desc">
                                    {{ Model.Product.Description | raw }}

                                    {% if Model.Product.Features != null and Model.Product.Features.size > 0 %}
                                    <div class="row">
                                        <div class="col-lg-5 col-xl-4">
                                            <div class="shop-single-list">
                                                <h5 class="title">Features</h5>
                                                <ul>
                                                    {% for feature in Model.Product.Features %}
                                                        <li>{{ feature }}</li>
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                        </div>
                                        {% endif %}

                                        {% if Model.Product.Specifications != null and Model.Product.Specifications.size > 0 %}
                                            <div class="col-lg-6 col-xl-5">
                                                <div class="shop-single-list">
                                                    <h5 class="title">Specifications</h5>
                                                    <ul>
                                                        {% for spec in Model.Product.Specifications %}
                                                            <li>
                                                                <span>{{ spec.Name }}:</span> {{ spec.Value }}
                                                            </li>
                                                        {% endfor %}
                                                    </ul>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- 如果是 Reviews 面板，显示原有的评论内容 -->
                            {% elsif switch.Identity == "Reviews" %}
                                <div class="shop-single-review">
                                    <div class="blog-comments">
                                        <div class="blog-comments-title">
                                            <h4>
                                                {% if Model.Product.ReviewCount > 0 %}{{ "blog.global.comments" | translate }}{% endif %}

                                            </h4>
                                        </div>
                                        <div class="blog-comments-content">
                                            {% if Model.Product.Reviews.size > 0 %}
                                                {% for review in Model.Product.Reviews %}
                                                    <div class="blog-comments-item">
                                                        <div class="blog-comments-img">
                                                            {% if review.UserAvatar %}
                                                                <img src="{{ review.UserAvatar }}"
                                                                     alt="{{ review.UserName }}">
                                                            {% else %}
                                                                <img src="/themes/t600/assets/img/icon-img/user.png"
                                                                     alt="{{ review.UserName }}">
                                                            {% endif %}
                                                        </div>
                                                        <div class="blog-comments-content">
                                                            <div class="blog-comments-top">
                                                                <div class="blog-comments-name">
                                                                    <h5>{{ review.UserName }}</h5>
                                                                    <span>{{ review.ReviewDate | date: '%Y-%m-%d' }}</span>
                                                                </div>
                                                                <div class="blog-rating">
                                                                    {% assign rating = review.Rating %}
                                                                    {% for i in (1..5) %}
                                                                        {% if i <= rating %}
                                                                            <i class="fas fa-star"></i>
                                                                        {% else %}
                                                                            <i class="far fa-star"></i>
                                                                        {% endif %}
                                                                    {% endfor %}
                                                                </div>
                                                            </div>
                                                            <p>{{ review.Content }}</p>

                                                            <!-- 显示评论图片 -->
                                                            {% if review.Images.size > 0 %}
                                                                <div class="review-images">
                                                                    {% for image in review.Images %}
                                                                        <a href="{{ image }}" class="review-image-item">
                                                                            <img src="{{ image }}" alt="评论图片">
                                                                        </a>
                                                                    {% endfor %}
                                                                </div>
                                                            {% endif %}

                                                            <!-- 显示回复（直接在主评论内容区域内） -->
                                                            {% if review.Replies.size > 0 %}
                                                                <div class="replies-container">
                                                                    {% for reply in review.Replies %}
                                                                        <div class="reply-item">
                                                                            <div class="reply-header">
                                                                                <strong>{{ reply.UserName }}</strong>
                                                                                {% if reply.IsAdmin %}<span
                                                                                        class="admin-badge">管理员</span>{% endif %}
                                                                                <span class="reply-date">{{ reply.ReplyDate | date: '%Y-%m-%d' }}</span>
                                                                            </div>
                                                                            <div class="reply-content">
                                                                                {% if reply.ReplyToName != null and reply.ReplyToName != '' %}
                                                                                    <span class="reply-to-tag">@{{ reply.ReplyToName }}</span>
                                                                                {% endif %}
                                                                                {{ reply.Content }}
                                                                            </div>
                                                                            <div class="reply-actions">
                                                                                <a href="javascript:void(0);"
                                                                                   onclick="replyToComment('{{ review.Id }}', '{{ reply.UserName }}', '{{ reply.Id }}')"
                                                                                   class="reply-btn">{{ "user.account.reply_btn" | translate }}</a>
                                                                            </div>
                                                                        </div>
                                                                    {% endfor %}
                                                                </div>
                                                            {% endif %}

                                                            <!-- 回复按钮 -->
                                                            <div class="blog-comments-btn">
                                                                <a href="javascript:void(0);"
                                                                   onclick="replyToComment('{{ review.Id }}', '{{ review.UserName }}')"
                                                                   data-review-id="{{ review.Id }}"
                                                                   data-user-name="{{ review.UserName }}">{{ "user.account.reply_btn" | translate }}</a>
                                                            </div>

                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            {% else %}
                                                <div class="no-reviews">
                                                    <p>
                                                        {{ "products.goods.no_review_data" | translate }}
                                                        . {{ "blog.global.leaveAReply" | translate }} !
                                                    </p>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="leave-form-area">
                                        <div class="blog-reply">
                                            <h4 class="comment-reply-title">{{ "blog.global.leaveAReply" | translate }}</h4>
                                            <form id="review-form" class="comment-form-area">
                                                <!-- 回复信息提示 -->
                                                <div id="reply-info"
                                                     style="display: none; margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                                                    <p>
                                                        回复给: <span id="reply-to-name" class="reply-to-tag"></span>
                                                        <a href="javascript:void(0);" onclick="cancelReply()"
                                                           style="float: right; color: #dc3545;">{{ "web.global.cancel" | translate }}</a>
                                                    </p>
                                                </div>

                                                <div class="row">
                                                    <div class="col-lg-6">
                                                        <div class="leave-form">
                                                            <input id="review-name"
                                                                   placeholder="{{ "products.goods.write_your_name" | translate }}*"
                                                                   type="text">
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-6">
                                                        <div class="leave-form">
                                                            <input id="review-email"
                                                                   placeholder="{{ "web.global.newsletter_your_email" | translate }}*"
                                                                   type="email">
                                                        </div>
                                                    </div>
                                                    <!--<div class="col-lg-12">
                                                        <div class="leave-form">
                                                            <input id="review-subject" placeholder="Your Subject*" type="text">
                                                        </div>
                                                    </div>-->
                                                    <div class="col-lg-12">
                                                        <div class="leave-form">
                                                            <select id="review-rating" class="form-control">
                                                                <option value="">{{ "products.goods.your_rating" | translate }}</option>
                                                                <option value="5">5 Stars</option>
                                                                <option value="4">4 Stars</option>
                                                                <option value="3">3 Stars</option>
                                                                <option value="2">2 Stars</option>
                                                                <option value="1">1 Star</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-12">
                                                        <div class="text-leave">
                                                <textarea id="review-content"
                                                          placeholder="{{ "products.goods.write_your_review" | translate }}*"></textarea>
                                                            <input id="captchaVerifyParam" type="hidden" value="">
                                                            <div id="captcha-element"></div>
                                                            <button id="review-submit-btn" class="submit" type="button"
                                                                    onclick="submitReview()">
                                                                {{ "products.goods.writeReview" | translate }}
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- 其他自定义面板，显示对应面板的内容 -->
                            {% else %}
                                <div class="shop-single-content">
                                    {% if switch.Content != null and switch.Content != "" %}
                                        <div class="content-body">
                                            {{ switch.Content | raw }}
                                        </div>
                                    {% else %}
                                        <p>No content available for this section.</p>
                                    {% endif %}
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>


        <!--End Product Tabs-->
        {% assign recommendproductslider = '/Themes/' | append: theme | append: '/Shop/RecommendProductSlider' %}
        {% include recommendproductslider, ecommendProducts: Model.RecommendProducts -%}

    </div>

</div>

<div class="modal quickview fade" id="quickview" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
     aria-labelledby="quickview" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                <i class="far fa-xmark"></i>
            </button>
            <div class="modal-body">
                <div class="row">
                    <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
                        <div class="quickview-img">
                            <img src="/assets/img/product/04.png" alt="#">
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
                        <div class="quickview-content">
                            <h4 class="quickview-title">Surgical Face Mask</h4>
                            <div class="quickview-rating">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                                <i class="far fa-star"></i>
                                <span class="rating-count"> (4 {{ "products.goods.customer_review" | translate }})</span>
                            </div>
                            <div class="quickview-price">
                                <h5>
                                    <del>$860</del>
                                    <span>$740</span>
                                </h5>
                            </div>
                            <ul class="quickview-list">
                                <li>Brand:<span>Medica</span></li>
                                <li>{{ "blog.global.blog_category" | translate }}:<span>Healthcare</span></li>
                                <li>
                                    {{ "user.account.order_status" | translate }}:<span class="stock">Available</span>
                                </li>
                                <li>{{ "products.goods.sku" | translate }}:<span>789FGDF</span></li>
                            </ul>
                            <div class="quickview-cart">
                                <a href="#" class="theme-btn">{{ "products.goods.addToCart" | translate }}</a>
                            </div>
                            <div class="quickview-social">
                                <span>{{ "user.account.DIST_how_to_share" | translate }}:</span>
                                <a href="#"><i class="fab fa-facebook-f"></i></a>
                                <a href="#"><i class="fab fa-x-twitter"></i></a>
                                <a href="#"><i class="fab fa-pinterest-p"></i></a>
                                <a href="#"><i class="fab fa-instagram"></i></a>
                                <a href="#"><i class="fab fa-linkedin-in"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Sticky Cart-->
<div class="stickyCart">
    <div class="container">
        <div class="img">
            <img src="{% if Model.Product.PicPath != null and Model.Product.PicPath != '' %}{{ Model.Product.PicPath }}{% else %}/assets/img/product/01.png{% endif %}"
                 class="product-featured-img"
                 alt="{{ Model.Product.ProductName }}">
        </div>
        <div class="sticky-title">
            <span>{{ Model.Product.ProductName }}</span>
            <div class="sticky-price-container">
                <span class="old-price" style="display: none;"></span>
                <span class="price">{{ Model.Product.PriceFormat }}</span>
            </div>
        </div>
        <div class="sticky-buttons">
            {% if Model.Product.IsInStock %}
                <button name="add" class="btn sticky-add-cart">
                    <span>{{ "products.goods.addToCart" | translate }}</span>
                </button>
                <button name="add" class="btn sticky-buy-now">
                    <span>{{ "products.goods.buyNow" | translate }}</span>
                </button>
            {% endif %}
        </div>
    </div>
</div>
<!--End Sticky Cart-->
<!-- 引入评论区域样式 -->
<link rel="stylesheet" href="/css/product/product-comments.css">
<!-- 引入阿里云验证码2.0 SDK -->
<script src="/js/AliyunCaptcha.js"></script>
<!-- 阿里云验证码核心模块 -->
<script src="/businessJs/aliyun-captcha-core.js"></script>
<!-- 引入自定义消息弹窗脚本 -->
<script src="/js/Pop-ups/frame-message.js"></script>
<!-- 公用价格处理工具 -->
<script src="/businessJs/Common/priceUtils.js"></script>
<!-- 价格显示功能模块 - t600主题专用 -->
<script src="/businessJs/Product/Single/priceDisplay_t600.js"></script>
<!-- Sticky Cart功能模块 - t600主题专用 -->
<script src="/businessJs/Product/Single/stickyCart_t600.js"></script>
<!-- 快速预览功能模块 -->
<script src="/businessJs/Product/Index/quickView.js"></script>

<script>
    // HTML实体解码函数
    function decodeHtmlEntities(str) {
        if (!str) return str;
        const textarea = document.createElement('textarea');
        textarea.innerHTML = str;
        return textarea.value;
    }

    // 产品变体数据（原始数据）
    const rawProductVariants = [
        {% if Model.Product.ProductVariants != null and Model.Product.ProductVariants.size > 0 %}
        {% for variant in Model.Product.ProductVariants %}
        {
            CId: "{{ variant.CId }}",
            OvId: "{{ variant.OvId }}",
            Sku: "{{ variant.SKU }}",
            VariantsId: "{{ variant.VariantsId }}",
            Title: "{{ variant.Title | escape }}",
            AttrName: "{{ variant.AttrName | escape }}",
            Price: {{ variant.Price | default: 0 }},
            PriceFormat: "{{ variant.PriceFormat | default: '' }}",
            OldPriceFormat: "{{ variant.OldPriceFormat | default: '' }}",
            PromotionPriceFormat: "{{ variant.PromotionPriceFormat | default: '' }}",
            Stock: {{ variant.Stock | default: 0 }},
            PicPath: "{{ variant.PicPath | default: '' }}"
        }{% unless forloop.last %},{% endunless %}
        {% endfor %}
        {% endif %}
    ];

    // 解码后的产品变体数据
    window.productVariants = rawProductVariants.map(variant => ({
        ...variant,
        Title: decodeHtmlEntities(variant.Title),
        AttrName: decodeHtmlEntities(variant.AttrName),
        PriceFormat: decodeHtmlEntities(variant.PriceFormat),
        OldPriceFormat: decodeHtmlEntities(variant.OldPriceFormat),
        PromotionPriceFormat: decodeHtmlEntities(variant.PromotionPriceFormat),
        PicPath: decodeHtmlEntities(variant.PicPath)
    }));

    // 存储用户实际选择的变体值（避免原生select状态不准确的问题）
    window.userSelectedVariants = {};

    // 调试信息输出
    setTimeout(() => {
        console.log('Product Variants:', window.productVariants);
        console.log('IsCombination:', '{{ Model.Product.IsCombination }}');
    }, 1000);


    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        // 初始化验证码（但不显示）
        initCaptcha();

        // 延迟初始化产品变体选择功能，确保nice-select已经初始化
        setTimeout(function () {
            initProductVariants();
        }, 500);


    });

    // 初始化验证码
    function initCaptcha() {
        // 确保YseCaptcha已加载
        if (typeof YseCaptcha === 'undefined') {
            console.error('YseCaptcha not loaded');
            return;
        }
        // 初始化验证码
        YseCaptcha.init({
            onSuccess: function () {
                // 验证成功后处理表单提交
                processFormSubmission();
            },
            onClose: function () {
                // 用户关闭或取消验证码时恢复按钮状态
                resetSubmitButton();
            }
        });
    }

    // 重置提交按钮状态
    function resetSubmitButton() {
        const submitBtn = document.getElementById('review-submit-btn');
        submitBtn.innerHTML = '{{ "products.goods.writeReview" | translate }}';
        submitBtn.disabled = false;
    }

    // 提交评论函数
    function submitReview() {
        // 获取表单数据
        const name = document.getElementById('review-name').value;
        const content = document.getElementById('review-content').value;
        const rating = document.getElementById('review-rating').value;

        // 验证表单数据
        if (!name.trim()) {
            customize_pop.warning('Please enter your name', null, null, {showIcon: false});
            return;
        }

        if (!content.trim()) {
            customize_pop.warning('Please enter your comment', null, null, {showIcon: false});
            return;
        }

        // 检查是否在回复模式
        const reviewForm = document.getElementById('review-form');
        const isReplyMode = reviewForm.dataset.replyTo;

        // 如果不是回复模式，需要验证邮箱和评分
        if (!isReplyMode) {
            const email = document.getElementById('review-email').value;
            if (!email.trim()) {
                customize_pop.warning('Please enter your email', null, null, {showIcon: false});
                return;
            }

            if (!rating || rating === '0') {
                customize_pop.warning('Please select a rating', null, null, {showIcon: false});
                return;
            }
        }

        // 显示提交中的状态
        const submitBtn = document.getElementById('review-submit-btn');
        submitBtn.innerHTML = 'Verifying...';
        submitBtn.disabled = true;

        // 触发验证码验证
        if (typeof YseCaptcha !== 'undefined') {
            YseCaptcha.verify();
        } else {
            customize_pop.error('Verification service not loaded, please refresh the page', null, null, {showIcon: false});
            resetSubmitButton();
        }
    }

    // 处理表单提交（验证码验证成功后）
    function processFormSubmission() {
        // 获取表单数据
        const name = document.getElementById('review-name').value;
        const email = document.getElementById('review-email').value;
        const content = document.getElementById('review-content').value;
        const rating = document.getElementById('review-rating').value;
        const captchaVerifyParam = YseCaptcha.getVerifyParam();
        const productId = '{{ Model.Product.ProductId }}';

        // 显示提交中的状态
        const submitBtn = document.getElementById('review-submit-btn');
        submitBtn.innerHTML = 'Submitting...';

        // 检查是否在回复评论
        const reviewForm = document.getElementById('review-form');
        const replyToId = reviewForm.dataset.replyTo;

        if (replyToId) {
            // 提交回复
            const replyData = {
                name: name,
                content: content,
                isAdmin: false,
                replyToId: parseInt(reviewForm.dataset.replyToId || "0"),
                replyToName: reviewForm.dataset.replyToName || "",
                captchaVerifyParam: captchaVerifyParam
            };

            // 发送回复请求
            fetch(`/api/product/review/${replyToId}/reply`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(replyData)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        customize_pop.success('Reply submitted successfully, thank you for your participation!', null, null, {showIcon: false});
                        // 重置表单
                        resetForm();
                        // 刷新页面以显示新回复
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        customize_pop.error(data.message || 'Failed to submit reply, please try again later', null, null, {showIcon: false});
                        // 恢复提交按钮
                        resetSubmitButton();
                    }
                })
                .catch(error => {
                    console.error('Error submitting reply:', error);
                    customize_pop.error('Failed to submit reply, please try again later', null, null, {showIcon: false});
                    // 恢复提交按钮
                    resetSubmitButton();
                });
        } else {
            // 提交评论
            const reviewData = {
                ProductId: productId,
                Name: name,
                Email: email,
                Title: 'Product Review',
                Content: content,
                Rating: parseInt(rating) || 0,
                CaptchaVerifyParam: captchaVerifyParam
            };

            // 发送评论请求
            fetch('/api/product/review/submit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(reviewData)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        customize_pop.success('Review submitted successfully, thank you for your participation!', null, null, {showIcon: false});
                        // 重置表单
                        resetForm();
                        // 刷新页面以显示新评论
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        customize_pop.error(data.message || 'Failed to submit review, please try again later', null, null, {showIcon: false});
                        // 恢复提交按钮
                        resetSubmitButton();
                    }
                })
                .catch(error => {
                    console.error('Error submitting review:', error);
                    customize_pop.error('Failed to submit review, please try again later', null, null, {showIcon: false});
                    // 恢复提交按钮
                    resetSubmitButton();
                });
        }
    }


    // 回复评论函数
    function replyToComment(reviewId, name, replyId) {
        // 滚动到评论表单
        const reviewForm = document.getElementById('review-form');
        reviewForm.scrollIntoView({behavior: 'smooth'});

        // 显示回复提示
        const replyInfo = document.getElementById('reply-info');
        const replyToName = document.getElementById('reply-to-name');
        replyInfo.style.display = 'block';
        replyToName.textContent = name;

        // 隐藏评分选择（回复模式下不需要评分）
        const ratingElement = document.getElementById('review-rating');
        if (ratingElement) {
            ratingElement.closest('.leave-form').style.display = 'none';
        }

        // 隐藏邮箱字段（回复模式下不需要邮箱）
        const emailElement = document.getElementById('review-email');
        if (emailElement) {
            emailElement.closest('.leave-form').style.display = 'none';
        }

        // 聚焦到表单
        document.getElementById('review-name').focus();

        // 存储正在回复的评论ID
        reviewForm.dataset.replyTo = reviewId;

        // 存储回复目标ID和名称（如果有）
        reviewForm.dataset.replyToId = replyId ? replyId : 0;
        reviewForm.dataset.replyToName = name || "";
    }

    // 取消回复函数
    function cancelReply() {
        // 隐藏回复提示
        document.getElementById('reply-info').style.display = 'none';

        // 重新显示评分选择（退出回复模式）
        const ratingElement = document.getElementById('review-rating');
        if (ratingElement) {
            ratingElement.closest('.leave-form').style.display = 'block';
        }

        // 重新显示邮箱字段（退出回复模式）
        const emailElement = document.getElementById('review-email');
        if (emailElement) {
            emailElement.closest('.leave-form').style.display = 'block';
        }

        // 清除回复ID和回复目标信息
        const reviewForm = document.getElementById('review-form');
        delete reviewForm.dataset.replyTo;
        reviewForm.dataset.replyToId = "0";
        reviewForm.dataset.replyToName = "";
    }

    // 重置表单函数
    function resetForm() {
        document.getElementById('review-name').value = '';
        document.getElementById('review-email').value = '';
        document.getElementById('review-content').value = '';
        document.getElementById('review-rating').value = '';

        // 重置验证码
        YseCaptcha.reset();

        // 取消回复模式
        cancelReply();
    }

    // 初始化产品变体选择功能
    function initProductVariants() {
        // 保存初始主图，以便在需要时恢复
        const initialMainImage = getInitialMainImage();
        window.initialMainImage = initialMainImage;

        console.log('Initial main image:', initialMainImage);

        // 确保主图在初始化时正确显示
        if (initialMainImage) {
            updateMainImage(initialMainImage);
        }

        // 处理颜色选择器（checkbox）- 确保只能选择一个
        document.addEventListener('change', function (event) {
            if (event.target.matches('.form-check-input[type="checkbox"]')) {
                const checkbox = event.target;
                const attributeName = checkbox.getAttribute('data-attribute');
                const optionName = checkbox.getAttribute('data-option-name');

                if (checkbox.checked) {
                    // 取消同一属性组中的其他选择，确保只能选择一个
                    const sameAttributeInputs = document.querySelectorAll(`input[data-attribute="${attributeName}"]`);
                    sameAttributeInputs.forEach(input => {
                        if (input !== checkbox) {
                            input.checked = false;
                        }
                    });

                    // 存储用户选择到全局变量
                    window.userSelectedVariants[attributeName] = optionName;
                } else {
                    // 从全局变量中移除选择
                    delete window.userSelectedVariants[attributeName];
                }

                updateVariantsCombination();
            }
        });

        // 处理nice-select下拉选择器 - 监听option点击事件
        document.addEventListener('click', function (event) {
            // 检查是否点击了nice-select的option
            if (event.target.matches('.nice-select .option')) {
                const clickedOption = event.target;
                const niceSelectContainer = clickedOption.closest('.nice-select');

                // 找到对应的原生select元素
                let originalSelect = null;
                if (niceSelectContainer && niceSelectContainer.previousElementSibling) {
                    originalSelect = niceSelectContainer.previousElementSibling;
                    if (originalSelect.tagName !== 'SELECT') {
                        originalSelect = null;
                    }
                }

                if (originalSelect && originalSelect.id === 'select_t600') {
                    // 获取点击的选项信息
                    const optionText = clickedOption.textContent.trim();
                    const attributeName = originalSelect.getAttribute('data-attribute');

                    // 直接从原生select中通过文本匹配找到正确的option
                    let correctOption = null;
                    for (let i = 0; i < originalSelect.options.length; i++) {
                        const option = originalSelect.options[i];
                        const optionDataName = option.getAttribute('data-option-name');
                        // 使用data-option-name匹配，因为这个更可靠
                        if (optionDataName === optionText) {
                            correctOption = option;
                            break;
                        }
                    }

                    if (correctOption) {
                        // 手动设置原生select的值
                        originalSelect.value = correctOption.value;

                        // 存储用户实际选择到全局变量
                        window.userSelectedVariants[attributeName] = optionText;
                    }

                    // 延迟执行更新
                    setTimeout(function () {
                        updateVariantsCombination();
                    }, 50);
                }
            }
        });

        // 处理原生select元素的change事件（作为备用机制）
        document.addEventListener('change', function (event) {
            if (event.target.matches('select[data-attribute]')) {
                const select = event.target;
                const selectedOption = select.options[select.selectedIndex];

                if (selectedOption && selectedOption.value) {
                    const attributeName = select.getAttribute('data-attribute');
                    const optionName = selectedOption.getAttribute('data-option-name') || selectedOption.textContent.trim();

                    if (attributeName && optionName && optionName !== 'Choose ' + attributeName) {
                        window.userSelectedVariants[attributeName] = optionName;
                        updateVariantsCombination();
                    }
                }
            }
        });

        // 初始化默认选择状态
        initDefaultSelections();

        // 初始化仓库选择功能
        initWarehouseSelection();

        // 初始化时也执行一次更新
        updateVariantsCombination();
    }

    // 获取初始主图路径
    function getInitialMainImage() {
        // 优先使用ProductImages集合中的第一张图片
        {% if Model.Product.ProductImages != null and Model.Product.ProductImages.size > 0 %}
        return '{{ Model.Product.ProductImages[0].PicPath }}';
        {% elsif Model.Product.PicPath != null and Model.Product.PicPath != "" %}
        return '{{ Model.Product.PicPath }}';
        {% else %}
        return '/assets/img/product/01.png';
        {% endif %}
    }

    // 初始化默认选择状态
    function initDefaultSelections() {
        // 处理所有已选中的颜色属性选项
        document.querySelectorAll('.form-check-input[type="checkbox"]:checked').forEach(checkbox => {
            const attributeName = checkbox.getAttribute('data-attribute');
            const optionName = checkbox.getAttribute('data-option-name');

            if (attributeName && optionName) {
                window.userSelectedVariants[attributeName] = optionName;
            }
        });

        // 处理所有已选中的下拉选择器选项
        document.querySelectorAll('select[data-attribute]').forEach(select => {
            const selectedOption = select.options[select.selectedIndex];
            if (selectedOption && selectedOption.value) {
                const attributeName = select.getAttribute('data-attribute');
                const optionName = selectedOption.getAttribute('data-option-name') || selectedOption.textContent.trim();

                if (attributeName && optionName && optionName !== 'Choose ' + attributeName) {
                    window.userSelectedVariants[attributeName] = optionName;
                }
            }
        });
    }

    // 初始化仓库选择功能
    function initWarehouseSelection() {
        // 仓库选择变化事件（处理checkbox类型的仓库选择）
        document.addEventListener('change', function (event) {
            if (event.target.matches('.warehouseInput[type="checkbox"]')) {
                const checkbox = event.target;
                const warehouseName = checkbox.getAttribute('data-warehouse-name');
                const warehouseId = checkbox.getAttribute('data-warehouse-id');

                if (checkbox.checked) {
                    // 取消其他仓库的选择，确保只能选择一个
                    const allWarehouseInputs = document.querySelectorAll('.warehouseInput[type="checkbox"]');
                    allWarehouseInputs.forEach(input => {
                        if (input !== checkbox) {
                            input.checked = false;
                        }
                    });

                    // 更新调试信息
                    const debugWarehouse = document.getElementById('debugWarehouse');
                    if (debugWarehouse) {
                        debugWarehouse.textContent = warehouseName + ' (ID: ' + warehouseId + ')';
                    }
                } else {
                    // 如果取消选择，清空调试信息
                    const debugWarehouse = document.getElementById('debugWarehouse');
                    if (debugWarehouse) {
                        debugWarehouse.textContent = '未选择';
                    }
                }

                // 仓库选择变化时也需要更新变体组合
                updateVariantsCombination();
            }
        });

        // 初始化仓库显示
        initWarehouseDisplay();
    }

    // 初始化仓库显示
    function initWarehouseDisplay() {
        // 获取选中的仓库（包括隐藏的单仓库情况）
        const selectedWarehouse = document.querySelector('.warehouseInput[checked], .warehouseInput:checked');

        if (selectedWarehouse) {
            const warehouseName = selectedWarehouse.getAttribute('data-warehouse-name');
            const warehouseId = selectedWarehouse.getAttribute('data-warehouse-id');

            // 更新调试信息
            const debugWarehouse = document.getElementById('debugWarehouse');
            if (debugWarehouse) {
                debugWarehouse.textContent = warehouseName + ' (ID: ' + warehouseId + ')';
            }
        } else {
            const debugWarehouse = document.getElementById('debugWarehouse');
            if (debugWarehouse) {
                debugWarehouse.textContent = '无可选仓库';
            }
        }
    }

    // 获取选中的仓库ID
    function getSelectedWarehouseId() {
        const selectedWarehouse = document.querySelector('.warehouseInput[checked], .warehouseInput:checked');
        return selectedWarehouse ? parseInt(selectedWarehouse.getAttribute('data-warehouse-id'), 10) : 0;
    }

    // 更新变体组合和匹配VariantsId
    function updateVariantsCombination() {
        // 获取产品的组合模式
        const isCombinationRaw = '{{ Model.Product.IsCombination }}';
        let isCombination = 1; // 默认为1（多规格模式）

        if (isCombinationRaw && isCombinationRaw !== '' && isCombinationRaw !== 'null' && isCombinationRaw !== 'undefined') {
            isCombination = parseInt(isCombinationRaw, 10);
            if (isNaN(isCombination)) {
                isCombination = 1;
            }
        }

        // 从全局变量获取所有用户选择的变体值
        const selectedVariants = [];

        // 遍历所有用户选择的变体
        for (const attributeName in window.userSelectedVariants) {
            const selectedValue = window.userSelectedVariants[attributeName];
            if (selectedValue) {
                selectedVariants.push(selectedValue.trim());
            }
        }

        // 生成组合字符串（用 / 分割）
        const combinationString = selectedVariants.join(' / ');
        let matchedVariantsId = '';

        if (window.productVariants && selectedVariants.length > 0) {
            if (isCombination === 0) {
                // 单规格模式：通常只有一个变体，直接使用第一个
                if (window.productVariants.length > 0) {
                    matchedVariantsId = window.productVariants[0].VariantsId || '';
                }
            } else if (isCombination === 2) {
                // 多规格加价模式：按属性组的固定顺序匹配，而不是按用户选择顺序
                const matchedIds = [];

                // 按属性组的顺序获取选中的值（先颜色，后尺寸）
                // 1. 先处理颜色属性
                const colorAttributes = ['颜色', 'Color', '色彩'];
                for (const attrName of colorAttributes) {
                    if (window.userSelectedVariants[attrName]) {
                        const selectedValue = window.userSelectedVariants[attrName];
                        let foundId = '';

                        // 先尝试精确匹配
                        for (const variant of window.productVariants) {
                            // 精确匹配Title
                            if (variant.Title && variant.Title.trim() === selectedValue) {
                                foundId = variant.VariantsId || '';
                                break;
                            }

                            // 精确匹配AttrName
                            if (variant.AttrName && variant.AttrName.trim() === selectedValue) {
                                foundId = variant.VariantsId || '';
                                break;
                            }
                        }

                        // 如果精确匹配没找到，再尝试部分匹配
                        if (!foundId) {
                            for (const variant of window.productVariants) {
                                if (variant.Title && variant.Title.includes(selectedValue)) {
                                    foundId = variant.VariantsId || '';
                                    break;
                                }
                            }
                        }

                        if (foundId) {
                            matchedIds.push(foundId);
                        }
                        break; // 找到颜色属性就跳出
                    }
                }

                // 2. 再处理尺寸属性
                const sizeAttributes = ['尺寸', 'Size', '大小'];
                for (const attrName of sizeAttributes) {
                    if (window.userSelectedVariants[attrName]) {
                        const selectedValue = window.userSelectedVariants[attrName];
                        let foundId = '';

                        // 先尝试精确匹配
                        for (const variant of window.productVariants) {
                            // 精确匹配Title
                            if (variant.Title && variant.Title.trim() === selectedValue) {
                                foundId = variant.VariantsId || '';
                                break;
                            }

                            // 精确匹配AttrName
                            if (variant.AttrName && variant.AttrName.trim() === selectedValue) {
                                foundId = variant.VariantsId || '';
                                break;
                            }
                        }

                        // 如果精确匹配没找到，再尝试部分匹配
                        if (!foundId) {
                            for (const variant of window.productVariants) {
                                if (variant.Title && variant.Title.includes(selectedValue)) {
                                    foundId = variant.VariantsId || '';
                                    break;
                                }
                            }
                        }

                        if (foundId) {
                            matchedIds.push(foundId);
                        }
                        break; // 找到尺寸属性就跳出
                    }
                }

                // 组合所有匹配的ID
                matchedVariantsId = matchedIds.join(',');
            } else if (isCombination === 1) {
                // 多规格模式：使用组合匹配逻辑，变体ID只有一个
                let matchedVariant = null;

                console.log('IsCombination=1 匹配调试:', {
                    selectedVariants,
                    productVariantsCount: window.productVariants.length
                });

                window.productVariants.forEach((variant, index) => {
                    if (!variant.Title && !variant.AttrName) {
                        return;
                    }

                    // 检查Title字段
                    let titleMatch = false;
                    if (variant.Title) {
                        titleMatch = checkVariantMatch(variant.Title, selectedVariants);
                        console.log(`变体${index} Title匹配:`, {
                            variantTitle: variant.Title,
                            selectedVariants,
                            titleMatch
                        });
                    }

                    // 检查AttrName字段
                    let attrNameMatch = false;
                    if (variant.AttrName) {
                        attrNameMatch = checkVariantMatch(variant.AttrName, selectedVariants);
                        console.log(`变体${index} AttrName匹配:`, {
                            variantAttrName: variant.AttrName,
                            selectedVariants,
                            attrNameMatch
                        });
                    }

                    if (titleMatch || attrNameMatch) {
                        matchedVariant = variant;
                        matchedVariantsId = variant.VariantsId || '';
                        console.log('找到匹配的变体:', variant);
                    }
                });
            }
        } else if (window.productVariants && selectedVariants.length === 0 && isCombination === 0) {
            // 单规格模式下，即使没有选择也可能需要返回默认变体
            if (window.productVariants.length > 0) {
                matchedVariantsId = window.productVariants[0].VariantsId || '';
            }
        }

        // 更新调试显示
        const debugSelectedAttributes = document.getElementById('debugSelectedAttributes');
        const debugVariantsId = document.getElementById('debugVariantsId');
        const hiddenCombination = document.getElementById('selectedVariantsCombination');
        const hiddenVariantsId = document.getElementById('matchedVariantsId');

        if (debugSelectedAttributes) {
            debugSelectedAttributes.textContent = combinationString || '未选择';
        }

        if (debugVariantsId) {
            debugVariantsId.textContent = matchedVariantsId || '未匹配';
        }

        // 更新隐藏字段
        if (hiddenCombination) {
            hiddenCombination.value = combinationString || '';
        }

        if (hiddenVariantsId) {
            hiddenVariantsId.value = matchedVariantsId || '';
        }

        // 调试信息
        console.log('Variant Update Debug:', {
            isCombination,
            combinationString,
            matchedVariantsId,
            selectedVariants: window.userSelectedVariants
        });

        // 更新SKU显示
        updateSkuDisplay(isCombination, matchedVariantsId);

        // 更新价格显示
        updatePriceDisplay(isCombination, matchedVariantsId);

        // 更新产品图片显示
        updateProductImages(matchedVariantsId);
    }

    // 更新SKU显示
    function updateSkuDisplay(isCombination, matchedVariantsId) {
        const skuSpan = document.querySelector('.shop-single-details-list li:nth-child(2) span');

        if (isCombination === 2) {
            // IsCombination为2时隐藏SKU字段
            const skuLi = document.querySelector('.shop-single-details-list li:nth-child(2)');
            if (skuLi) {
                skuLi.style.display = 'none';
            }
        } else {
            // IsCombination为0或1时显示SKU字段
            const skuLi = document.querySelector('.shop-single-details-list li:nth-child(2)');
            if (skuLi) {
                skuLi.style.display = 'list-item';
            }

            // 根据VariantsId更新SKU显示
            if (matchedVariantsId && window.productVariants) {
                // 查找匹配的变体
                const matchedVariant = window.productVariants.find(variant =>
                    variant.VariantsId === matchedVariantsId
                );

                if (matchedVariant && matchedVariant.Sku) {
                    // 更新SKU显示
                    if (skuSpan) {
                        skuSpan.textContent = matchedVariant.Sku;
                    }
                } else {
                    // 如果没有找到匹配的变体，显示默认SKU
                    if (skuSpan) {
                        skuSpan.textContent = '{{ Model.Product.Sku }}';
                    }
                }
            } else {
                // 如果没有匹配的VariantsId，显示默认SKU
                if (skuSpan) {
                    skuSpan.textContent = '{{ Model.Product.Sku }}';
                }
            }
        }
    }

    // 更新价格显示
    function updatePriceDisplay(isCombination, matchedVariantsId) {
        // 获取基础价格（产品默认价格）并解码HTML实体
        const basePriceFormat = decodeHtmlEntities('{{ Model.Product.PriceFormat }}');
        const baseOriginalPriceFormat = decodeHtmlEntities('{{ Model.Product.OriginalPriceFormat }}');
        const basePromotionPriceFormat = decodeHtmlEntities('{{ Model.Product.PromotionPriceFormat }}');

        // 获取价格显示元素
        const currentPriceSpan = document.querySelector('.shop-single-price .amount');
        const originalPriceSpan = document.querySelector('.shop-single-price del');
        const promotionPriceSpan = document.querySelector('.shop-single-price span[style*="color: var(--color-red2)"]');

        // 备用选择器（如果主选择器找不到元素）
        const backupCurrentPriceSpan = document.querySelector('.shop-single-price span:not(del):not([style])');
        const backupOriginalPriceSpan = document.querySelector('.shop-single-price del');

        // 查找促销价格元素（通过遍历查找包含"Promotional Price"的div）
        let backupPromotionPriceSpan = null;
        const allDivs = document.querySelectorAll('div');
        for (const div of allDivs) {
            if (div.textContent && div.textContent.includes('Promotional Price')) {
                backupPromotionPriceSpan = div.querySelector('span[style*="color"]');
                break;
            }
        }

        let finalPriceFormat = basePriceFormat;
        let finalOriginalPriceFormat = baseOriginalPriceFormat;
        let finalPromotionPriceFormat = basePromotionPriceFormat;

        if (window.productVariants && window.productVariants.length > 0) {
            if (isCombination === 0 || isCombination === 1) {
                // IsCombination为0或1时，直接根据VariantsId匹配对应的价格
                let matchedVariant = null;

                if (matchedVariantsId) {
                    // 如果有匹配的VariantsId，优先使用匹配的变体
                    matchedVariant = window.productVariants.find(variant =>
                        variant.VariantsId === matchedVariantsId
                    );
                }

                // 如果没有找到匹配的变体，或者在单规格模式下VariantsId为空，使用第一个变体
                if (!matchedVariant && (isCombination === 0 || !matchedVariantsId)) {
                    matchedVariant = window.productVariants[0];
                }

                if (matchedVariant) {
                    // 优先使用变体的价格，如果变体价格为空或"0"才使用基础价格
                    finalPriceFormat = (matchedVariant.PriceFormat && matchedVariant.PriceFormat !== "0") ? matchedVariant.PriceFormat : basePriceFormat;
                    finalOriginalPriceFormat = (matchedVariant.OldPriceFormat && matchedVariant.OldPriceFormat !== "0") ? matchedVariant.OldPriceFormat : baseOriginalPriceFormat;
                    finalPromotionPriceFormat = (matchedVariant.PromotionPriceFormat && matchedVariant.PromotionPriceFormat !== "0") ? matchedVariant.PromotionPriceFormat : basePromotionPriceFormat;
                }
            } else if (isCombination === 2) {
                // IsCombination为2时，需要累加计算价格
                const variantIds = matchedVariantsId.split(',');
                let additionalPrice = 0;
                let additionalOriginalPrice = 0;
                let additionalPromotionPrice = 0;

                // 循环匹配每个VariantsId并累加价格
                variantIds.forEach(variantId => {
                    const matchedVariant = window.productVariants.find(variant =>
                        variant.VariantsId === variantId.trim()
                    );

                    if (matchedVariant) {
                        // 使用智能价格提取函数处理不同货币格式
                        const variantPrice = extractPriceValue(matchedVariant.PriceFormat);
                        const variantOriginalPrice = extractPriceValue(matchedVariant.OldPriceFormat);
                        const variantPromotionPrice = extractPriceValue(matchedVariant.PromotionPriceFormat);

                        additionalPrice += variantPrice;
                        additionalOriginalPrice += variantOriginalPrice;
                        additionalPromotionPrice += variantPromotionPrice;
                    }
                });

                // 计算最终价格：基础价 + 累加价
                const basePrice = extractPriceValue(basePriceFormat);
                const baseOriginalPrice = extractPriceValue(baseOriginalPriceFormat);
                const basePromotionPrice = extractPriceValue(basePromotionPriceFormat);

                const finalPrice = basePrice + additionalPrice;
                const finalOriginalPrice = baseOriginalPrice + additionalOriginalPrice;
                const finalPromotionPrice = basePromotionPrice + additionalPromotionPrice;

                // 格式化最终价格（保持原有的货币符号格式）
                const currencySymbolRaw = basePriceFormat.replace(/[0-9.-]+/g, '').trim();
                const currencySymbol = decodeHtmlEntities(currencySymbolRaw);
                finalPriceFormat = currencySymbol + finalPrice.toFixed(2);
                finalOriginalPriceFormat = currencySymbol + finalOriginalPrice.toFixed(2);
                finalPromotionPriceFormat = currencySymbol + finalPromotionPrice.toFixed(2);
            }
        }

        // 更新价格显示
        const priceSpanToUpdate = currentPriceSpan || backupCurrentPriceSpan;
        const originalSpanToUpdate = originalPriceSpan || backupOriginalPriceSpan;
        const promotionSpanToUpdate = promotionPriceSpan || backupPromotionPriceSpan;

        if (priceSpanToUpdate) {
            priceSpanToUpdate.textContent = finalPriceFormat;
        }

        if (originalSpanToUpdate) {
            originalSpanToUpdate.textContent = finalOriginalPriceFormat;
        }

        if (promotionSpanToUpdate) {
            promotionSpanToUpdate.textContent = finalPromotionPriceFormat;
        }

        // 调试信息
        console.log('Price Update Debug:', {
            finalPriceFormat,
            finalOriginalPriceFormat,
            finalPromotionPriceFormat,
            currentPriceSpan: !!priceSpanToUpdate,
            originalPriceSpan: !!originalSpanToUpdate,
            promotionPriceSpan: !!promotionSpanToUpdate
        });

        // 更新价格显示模块
        if (window.PriceDisplayT600 && window.PriceDisplayT600.updatePriceDisplay) {
            window.PriceDisplayT600.updatePriceDisplay({
                PriceFormat: finalPriceFormat,
                OriginalPriceFormat: finalOriginalPriceFormat,
                PromotionPriceFormat: finalPromotionPriceFormat
            });
        }

        // 更新sticky cart的价格显示
        if (window.StickyCartT600 && window.StickyCartT600.updatePriceDisplay) {
            window.StickyCartT600.updatePriceDisplay({
                PriceFormat: finalPriceFormat,
                OriginalPriceFormat: finalOriginalPriceFormat,
                PromotionPriceFormat: finalPromotionPriceFormat
            });
        }
    }

    // 从格式化价格字符串中提取数值
    function extractPriceValue(priceString) {
        if (!priceString) return 0;

        // 移除所有非数字和小数点的字符
        const numericString = priceString.replace(/[^\d.-]/g, '');
        const value = parseFloat(numericString);

        return isNaN(value) ? 0 : value;
    }

    // 检查变体是否匹配（不区分顺序）
    function checkVariantMatch(variantString, selectedVariants) {
        if (!variantString || selectedVariants.length === 0) {
            console.log('checkVariantMatch: 空参数', { variantString, selectedVariants });
            return false;
        }

        // 将变体字符串按 / 分割并清理空格，同时解码HTML实体
        const variantParts = variantString.split('/').map(part => decodeHtmlEntities(part.trim())).filter(part => part);

        console.log('checkVariantMatch 调试:', {
            variantString,
            variantParts,
            selectedVariants,
            lengthMatch: variantParts.length === selectedVariants.length
        });

        // 检查长度是否一致
        if (variantParts.length !== selectedVariants.length) {
            return false;
        }

        // 检查是否所有选中的变体都在变体字符串中
        const allMatch = selectedVariants.every(selected => {
            // 先尝试精确匹配
            let found = variantParts.some(part => part === selected);

            // 如果精确匹配失败，尝试部分匹配（包含关系）
            if (!found) {
                found = variantParts.some(part =>
                    part.includes(selected) || selected.includes(part)
                );
            }

            console.log(`检查选中项 "${selected}" 是否在变体部分中:`, {
                found,
                variantParts,
                exactMatch: variantParts.some(part => part === selected),
                partialMatch: variantParts.some(part => part.includes(selected) || selected.includes(part))
            });
            return found;
        });

        console.log('最终匹配结果:', allMatch);
        return allMatch;
    }

    // 更新产品图片显示
    function updateProductImages(matchedVariantsId) {
        // 获取默认主图路径（优先使用保存的初始图片）
        const defaultMainImage = window.initialMainImage || getInitialMainImage();

        if (!matchedVariantsId || !window.productVariants) {
            // 如果没有匹配的变体ID，显示默认主图
            if (defaultMainImage) {
                updateMainImage(defaultMainImage);
                updateThumbnailSelection(defaultMainImage);
            }
            return;
        }

        // 查找匹配的变体
        const matchedVariant = window.productVariants.find(variant =>
            variant.VariantsId === matchedVariantsId
        );

        // 如果找到匹配的变体且有图片路径
        if (matchedVariant && matchedVariant.PicPath && matchedVariant.PicPath.trim() !== '') {
            const picPath = matchedVariant.PicPath.trim();

            // 更新主图
            updateMainImage(picPath);

            // 更新缩略图选中状态
            updateThumbnailSelection(picPath);
        } else {
            console.log('Variant has no image, using default image');
            // 如果变体没有图片，回退到显示默认主图
            if (defaultMainImage) {
                updateMainImage(defaultMainImage);
                updateThumbnailSelection(defaultMainImage);
            }
        }
    }

    // 更新主图显示
    function updateMainImage(picPath) {
        // 更新flexslider中的主图
        const mainImages = document.querySelectorAll('.flexslider-thumbnails .slides li img');
        if (mainImages.length > 0) {
            // 更新第一张图片（主图）
            mainImages[0].src = picPath;
            mainImages[0].alt = '{{ Model.Product.ProductName }}';
        }

        // 同时更新sticky cart中的图片
        const stickyImage = document.querySelector('.stickyCart .product-featured-img');
        if (stickyImage) {
            stickyImage.src = picPath;
            stickyImage.alt = '{{ Model.Product.ProductName }}';
        }

        // 通过sticky cart的API更新图片
        if (window.StickyCartT600 && window.StickyCartT600.updateProductImage) {
            window.StickyCartT600.updateProductImage(picPath);
        }
    }

    // 更新缩略图选中状态
    function updateThumbnailSelection(picPath) {
        // t600主题使用flexslider，这里可以添加缩略图选中状态的更新逻辑
        // 由于flexslider的复杂性，这里暂时保留空实现
        // 如果需要实现缩略图选中状态，需要与flexslider的API集成
    }

    //跳转评论
    document.querySelector(".rating-count").addEventListener('click', function () {
        // 隐藏所有 tab-content
        document.querySelectorAll('.tab-pane').forEach(function (content) {
            var dataId = content.getAttribute('data-id');
            if (dataId == "reviews") {
                content.classList.add('show');
                content.classList.add('active');
            } else {
                content.classList.remove('show');
                content.classList.remove('active');
            }
        });
        document.querySelectorAll('.nav-tabs button').forEach(function (tab) {
            var dataId = tab.getAttribute('data-id');
            // 获取当前 tab 的 rel 属性
            var activeTab = tab.getAttribute('data-bs-target');
            // 移除所有 tab 的 active 类
            document.querySelectorAll('.nav-tabs button').forEach(function (li) {
                li.classList.remove('active');
            });
            if (dataId == "reviews") {
                // 当前 tab 添加 active 类
                tab.classList.add('active');
            }
        });
    })

    // 添加到购物车功能
    function addToCart() {
        const quantity = document.querySelector('.quantity').value || 1;
        const variantsId = document.getElementById('matchedVariantsId').value || '';
        const ovId = getSelectedWarehouseId();
        const productId = parseInt('{{ Model.Product.ProductId }}', 10);

        const addCartData = {
            ProductId: productId,
            variantsId: variantsId,
            Nums: parseInt(quantity, 10),
            cartType: 1,
            ovId: ovId
        };

        // 发送加购数据到服务器
        fetch('/Cart/AddCart', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(addCartData)
        })
            .then(response => response.json())
            .then(data => {
                if (data.status) {
                    //更新购物车数量角标
                    $(".site-cart-count").text(data.otherData);

                    customize_pop.success(data.msg || '添加成功', null, null, {showIcon: false});
                } else {
                    customize_pop.error('Failed to add to cart: ' + (data.msg || 'Unknown error'), null, null, {showIcon: true});
                }
            })
            .catch(error => {
                customize_pop.error('Please try again later.', null, null, {showIcon: true});
            });
    }


    //立即购买
    function buyItNow() {

        const quantity = document.querySelector('.quantity').value || 1;
        const variantsId = document.getElementById('matchedVariantsId').value || '';
        var cartType = 2;
        //variantsId ="10050,10052"
        var ovId = getSelectedWarehouseId(); // 使用选中的仓库ID
        var ProductId = parseInt('{{ Model.Product.ProductId }}', 10);

        const addCartData = {
            ProductId: ProductId,
            variantsId: variantsId,
            Nums: parseInt(quantity, 10),
            cartType: cartType,
            ovId: ovId,
        };

        //var param = { entity: addCartData };

        //console.log(param);

        // 发送加购数据到服务器
        $.ajax({
            url: '/Cart/BuyItNow',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(addCartData),
            success: function (data) {
                //debugger;
                // 关闭加载提示
                customize_pop.loadingClose();

                var msg = data.msg;
                if (msg != '') {
                    msg = data.msg;
                }
                if (data.status) {
                    //更新购物车数量角标
                    $(".site-cart-count").text(data.otherData);

                    //结算
                    window.location.href = data.data.location;

                } else {
                    customize_pop.error('Failed to buy it now: ' + (data.msg || 'Unknown error'), null, null, {showIcon: true});
                }
            },
            error: function (xhr, status, error) {
                // 关闭加载提示
                customize_pop.loadingClose();
                customize_pop.error('Please try again later.', null, null, {showIcon: true});
            },
            complete: function () {
                // 可以在这里添加完成后的处理逻辑
            }
        });
    };


    // 初始化购物车按钮事件
    document.addEventListener('DOMContentLoaded', function () {
        const addToCartBtn = document.querySelector('.add-to-cart-btn');
        if (addToCartBtn) {
            addToCartBtn.addEventListener('click', function (e) {
                e.preventDefault();
                addToCart();
            });
        }
    });

    // 收藏功能处理
    function handleWishlistClick(e) {
        const wishlistBtn = e.target.closest('.add-to-wishlist');
        if (wishlistBtn) {
            e.preventDefault();
            e.stopPropagation();

            // 获取产品ID和收藏状态
            const productId = wishlistBtn.getAttribute('data-product-id');
            const isFavorited = wishlistBtn.getAttribute('data-is-favorited') === 'true';

            if (!productId) {
                if (typeof customize_pop !== 'undefined' && customize_pop.warning) {
                    customize_pop.warning('Unable to get product ID', null, null, {showIcon: false});
                } else {
                    alert('Unable to get product ID');
                }
                return;
            }

            // 根据当前收藏状态决定操作
            const url = isFavorited ? '/Account/RemoveFromWishlistByProductId' : '/Account/AddToWishlist';
            const requestBody = {productId: parseInt(productId)};

            // 发送收藏请求
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 根据操作类型显示不同的成功消息
                    const message = isFavorited ? 'Removed from wishlist successfully' : 'Added to wishlist successfully';
                    if (typeof customize_pop !== 'undefined' && customize_pop.success) {
                        customize_pop.success(message, null, null, {showIcon: false});
                    } else {
                        alert(message);
                    }

                    // 更新按钮状态
                    const heartIcon = wishlistBtn.querySelector('span');
                    if (heartIcon) {
                        if (isFavorited) {
                            heartIcon.className = 'far fa-heart'; // 改为空心心形
                            wishlistBtn.setAttribute('data-is-favorited', 'false');
                        } else {
                            heartIcon.className = 'fas fa-heart'; // 改为实心心形
                            wishlistBtn.setAttribute('data-is-favorited', 'true');
                        }
                    }
                } else {
                    if (typeof customize_pop !== 'undefined' && customize_pop.warning) {
                        customize_pop.warning(data.message || 'Operation failed', null, null, {showIcon: false});
                    } else {
                        alert(data.message || 'Operation failed');
                    }
                }
            })
            .catch(error => {
                console.error('收藏操作时出错:', error);
                if (typeof customize_pop !== 'undefined' && customize_pop.error) {
                    customize_pop.error('Network error, please try again later', null, null, {showIcon: false});
                } else {
                    alert('Network error, please try again later');
                }
            });
        }
    }

    // 绑定收藏按钮点击事件
    document.addEventListener('click', handleWishlistClick);

    // 确保函数全局可访问
    window.replyToComment = replyToComment;
    window.cancelReply = cancelReply;
    window.submitReview = submitReview;
    window.initProductVariants = initProductVariants;
    window.updateVariantsCombination = updateVariantsCombination;
    window.addToCart = addToCart;
    window.buyItNow = buyItNow;
    window.getSelectedWarehouseId = getSelectedWarehouseId;
    window.updateProductImages = updateProductImages;
    window.updateMainImage = updateMainImage;
    window.updatePriceDisplay = updatePriceDisplay;
    window.extractPriceValue = extractPriceValue;
    window.handleWishlistClick = handleWishlistClick;
</script>

<!-- 添加自定义样式 -->
<style>
    /* 收藏按钮样式 */
    .add-to-wishlist {
        transition: all 0.3s ease;
        position: relative;
    }

    .add-to-wishlist:hover {
        transform: scale(1.05);
    }

    .add-to-wishlist span {
        transition: color 0.3s ease;
    }

    .add-to-wishlist[data-is-favorited="true"] span {
        color: #e74c3c !important;
    }

    .add-to-wishlist[data-is-favorited="false"] span {
        color: inherit;
    }

    /* 仓库选择样式 */
    .shop-single-warehouse {
        margin-bottom: 20px;
    }

    .shop-single-warehouse h6 {
        margin-bottom: 15px;
        font-weight: 600;
        color: #333;
    }

    .shop-checkbox-list.warehouse {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .shop-checkbox-list.warehouse li {
        margin: 0;
    }

    .shop-checkbox-list.warehouse .form-check {
        margin: 0;
        padding: 0;
    }

    .shop-checkbox-list.warehouse .form-check-input {
        display: none;
    }

    .shop-checkbox-list.warehouse .form-check-label {
        display: inline-block;
        padding: 8px 16px;
        border: 2px solid #e9ecef;
        border-radius: 5px;
        background-color: #fff;
        color: #333;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 0;
    }

    .shop-checkbox-list.warehouse .form-check-label:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }

    .shop-checkbox-list.warehouse .form-check-input:checked + .form-check-label {
        border-color: #007bff;
        background-color: #007bff;
        color: #fff;
    }

    .single-warehouse {
        display: inline-block;
        padding: 8px 16px;
        border: 2px solid #28a745;
        border-radius: 5px;
        background-color: #28a745;
        color: #fff;
        font-weight: 500;
    }

    /* 回复容器样式 */
    .replies-container {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #e9ecef;
    }

    /* StickyCart样式 */
    .stickyCart {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        border-top: 1px solid #e9ecef;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        display: none;
        opacity: 0;
        transform: translateY(100%);
        transition: all 0.3s ease;
    }

    .stickyCart .container {
        display: flex;
        align-items: center;
        padding: 15px 0;
        gap: 15px;
    }

    .stickyCart .img {
        flex-shrink: 0;
    }

    .stickyCart .img img {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 5px;
    }

    .stickyCart .sticky-title {
        flex: 1;
        min-width: 0;
    }

    .stickyCart .sticky-title span {
        display: block;
        font-size: 14px;
        line-height: 1.4;
    }

    .stickyCart .sticky-title > span {
        font-weight: 600;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 5px;
    }

    .stickyCart .sticky-price-container {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .stickyCart .old-price {
        text-decoration: line-through;
        color: #999;
        font-size: 13px;
    }

    .stickyCart .price {
        color: #007bff;
        font-weight: 600;
        font-size: 14px;
    }

    .stickyCart .sticky-buttons {
        display: flex;
        gap: 10px;
        flex-shrink: 0;
    }

    .stickyCart button {
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
    }

    .stickyCart .sticky-add-cart {
        background: #fff;
        color: #007bff;
        border: 2px solid #007bff;
    }

    .stickyCart .sticky-add-cart:hover {
        background: #007bff;
        color: #fff;
    }

    .stickyCart .sticky-buy-now {
        background: #007bff;
        color: #fff;
    }

    .stickyCart .sticky-buy-now:hover {
        background: #0056b3;
    }

    /* 回复项样式 */
    .reply-item {
        background-color: #f8f9fa;
        border-left: 3px solid #007bff;
        padding: 12px 15px;
        margin-bottom: 10px;
        border-radius: 0 5px 5px 0;
    }

    /* 回复头部样式 */
    .reply-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;
    }

    .reply-header strong {
        color: #333;
        margin-right: 8px;
    }

    .reply-date {
        color: #6c757d;
        font-size: 12px;
        margin-left: auto;
    }

    /* 回复内容样式 */
    .reply-content {
        color: #555;
        line-height: 1.5;
        margin-bottom: 8px;
    }

    /* 回复操作样式 */
    .reply-actions {
        text-align: right;
    }

    .reply-btn {
        color: #007bff;
        text-decoration: none;
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 3px;
        transition: all 0.3s ease;
    }

    .reply-btn:hover {
        background-color: #e7f3ff;
        color: #0056b3;
    }

    /* 回复标签样式 */
    .reply-to-tag {
        color: #007bff;
        font-weight: 600;
        margin-right: 5px;
    }

    /* 回复按钮样式 */
    .blog-comments-btn a {
        display: inline-block;
        padding: 5px 10px;
        margin-top: 5px;
        color: #0056b3;
        border-radius: 3px;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .blog-comments-btn a:hover {
        background-color: #e9ecef;
    }

    /* 管理员标记样式 */
    .admin-badge {
        background-color: #28a745;
        color: white;
        padding: 2px 5px;
        border-radius: 3px;
        font-size: 11px;
        margin-left: 5px;
        vertical-align: middle;
    }

    /* 移除旧的回复样式 */
    .blog-comments-reply {
        display: none;
    }
</style>

<!-- 导入图片处理助手 -->
{% comment %}<script src="/businessJs/imageUrlHelper.js"></script>{% endcomment %}

{% comment %}<script>{% endcomment %}
{% comment %}// 页面加载完成后处理所有图片URL{% endcomment %}
{% comment %}document.addEventListener('DOMContentLoaded', function() {{% endcomment %}
    {% comment %}// 处理产品详情页面中的所有图片{% endcomment %}
    {% comment %}const productDetailImages = document.querySelectorAll('.shop-single-gallery img, .flexslider-thumbnails img');{% endcomment %}
    {% comment %}productDetailImages.forEach(function(img) {{% endcomment %}
        {% comment %}if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {{% endcomment %}
            {% comment %}img.src = ImageUrlHelper.getMediumUrl(img.src);{% endcomment %}
        {% comment %}}{% endcomment %}
        {% comment %}if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {{% endcomment %}
            {% comment %}img.dataset.src = ImageUrlHelper.getMediumUrl(img.dataset.src);{% endcomment %}
        {% comment %}}{% endcomment %}
        {% comment %}if (img.dataset.thumb && !ImageUrlHelper.hasOssParams(img.dataset.thumb)) {{% endcomment %}
            {% comment %}img.dataset.thumb = ImageUrlHelper.getMediumUrl(img.dataset.thumb);{% endcomment %}
        {% comment %}}{% endcomment %}
    {% comment %}});{% endcomment %}

    {% comment %}// 处理快速预览模态框中的图片{% endcomment %}
    {% comment %}const quickViewImages = document.querySelectorAll('#quickview img');{% endcomment %}
    {% comment %}quickViewImages.forEach(function(img) {{% endcomment %}
        {% comment %}if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {{% endcomment %}
            {% comment %}img.src = ImageUrlHelper.getMediumUrl(img.src);{% endcomment %}
        {% comment %}}{% endcomment %}
        {% comment %}if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {{% endcomment %}
            {% comment %}img.dataset.src = ImageUrlHelper.getMediumUrl(img.dataset.src);{% endcomment %}
        {% comment %}}{% endcomment %}
    {% comment %}});{% endcomment %}
{% comment %}});{% endcomment %}
{% comment %}</script>{% endcomment %}

